import React, { useState, useEffect } from 'react';
import { Alert, Platform } from 'react-native';
import { router } from 'expo-router';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonText,
  Input,
  InputField,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  SafeAreaView,
  KeyboardAvoidingView,
  ScrollView,
  Pressable,
} from '@/components/ui';
import { useAuth } from '@/src/stores/authStore';
import { useForm } from '@/src/hooks/useForm';
import { ValidationUtils } from '@/src/utils/validation';
import { SecurityUtils } from '@/src/utils/security';
import { StorageUtils } from '@/src/utils/storage';
import { LoadingSpinner } from '@/src/components/LoadingSpinner';

interface LoginFormData {
  phoneNumber: string;
  pin: string;
}

export default function LoginScreen() {
  const { login, isLoading, error, clearError } = useAuth();
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [biometricEnabled, setBiometricEnabled] = useState(false);

  const {
    values,
    errors,
    isValid,
    setValue,
    validate,
    handleSubmit,
  } = useForm<LoginFormData>({
    initialValues: {
      phoneNumber: '',
      pin: '',
    },
    validationRules: {
      phoneNumber: ValidationUtils.validatePhoneNumber,
      pin: ValidationUtils.validatePin,
    },
    onSubmit: handleLogin,
  });

  useEffect(() => {
    checkBiometricAvailability();
    checkBiometricSettings();
  }, []);

  const checkBiometricAvailability = async () => {
    const available = await SecurityUtils.isBiometricAvailable();
    setBiometricAvailable(available);
  };

  const checkBiometricSettings = async () => {
    const enabled = await StorageUtils.isBiometricEnabled();
    setBiometricEnabled(enabled);
  };

  async function handleLogin(formData: LoginFormData) {
    clearError();
    
    const success = await login(formData);
    
    if (success) {
      // Save phone number for biometric login
      await StorageUtils.setItem('lastLoginPhone', formData.phoneNumber);
      router.replace('/(main)/(tabs)/home');
    } else {
      Alert.alert('Login Failed', error || 'Please check your credentials and try again.');
    }
  }

  const handleBiometricLogin = async () => {
    if (!biometricAvailable || !biometricEnabled) {
      Alert.alert('Biometric Login', 'Biometric authentication is not available or enabled.');
      return;
    }

    const result = await SecurityUtils.authenticateWithBiometrics('Login to OneMoney');
    
    if (result.success) {
      // In a real app, you would retrieve stored credentials or use a token
      const savedPhoneNumber = await StorageUtils.getItem('lastLoginPhone');
      if (savedPhoneNumber) {
        setValue('phoneNumber', savedPhoneNumber);
        // Auto-login with biometric success
        router.replace('/(main)/(tabs)/home');
      }
    } else {
      Alert.alert('Authentication Failed', result.error || 'Biometric authentication failed.');
    }
  };

  const handleForgotPin = () => {
    router.push('/(auth)/forgot-pin');
  };

  const handleRegister = () => {
    router.push('/(auth)/register');
  };

  if (isLoading) {
    return <LoadingSpinner message="Signing you in..." overlay />;
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1">
        <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
          <VStack className="flex-1 px-6 py-8" space="lg">
            {/* Logo Section */}
            <VStack className="items-center mt-16 mb-8" space="md">
              <Box className="w-32 h-32 bg-blue-100 rounded-full items-center justify-center">
                <Text className="text-4xl font-bold text-blue-600">OM</Text>
              </Box>
              <Text className="text-2xl font-bold text-gray-800">OneMoney</Text>
              <Text className="text-gray-600 text-center">
                Your digital wallet for seamless transactions
              </Text>
            </VStack>

            {/* Form Section */}
            <VStack space="lg" className="flex-1">
              <FormControl isInvalid={!!errors.phoneNumber}>
                <FormControlLabel>
                  <FormControlLabelText>Phone Number</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="Enter your phone number"
                    value={values.phoneNumber}
                    onChangeText={(text) => setValue('phoneNumber', text)}
                    keyboardType="phone-pad"
                    autoCapitalize="none"
                    onBlur={() => validate('phoneNumber')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.phoneNumber}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.pin}>
                <FormControlLabel>
                  <FormControlLabelText>PIN</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="Enter your 4-digit PIN"
                    value={values.pin}
                    onChangeText={(text) => setValue('pin', text)}
                    keyboardType="numeric"
                    secureTextEntry
                    maxLength={4}
                    onBlur={() => validate('pin')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.pin}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <Pressable onPress={handleForgotPin}>
                <Text className="text-blue-600 text-right">Forgot PIN?</Text>
              </Pressable>

              <Button
                onPress={handleSubmit}
                isDisabled={isLoading || !isValid}
                className="mt-4"
              >
                <ButtonText>{isLoading ? 'Signing In...' : 'Sign In'}</ButtonText>
              </Button>

              {/* Biometric Login */}
              {biometricAvailable && biometricEnabled && (
                <VStack className="items-center mt-4" space="sm">
                  <Text className="text-gray-600">or</Text>
                  <Pressable onPress={handleBiometricLogin}>
                    <Box className="w-16 h-16 bg-blue-100 rounded-full items-center justify-center">
                      <Text className="text-2xl">👆</Text>
                    </Box>
                  </Pressable>
                  <Text className="text-blue-600 text-sm">Use Biometric Login</Text>
                </VStack>
              )}
            </VStack>

            {/* Register Section */}
            <VStack className="items-center mt-8" space="md">
              <Text className="text-gray-600">Don't have an account?</Text>
              <Pressable onPress={handleRegister}>
                <Text className="text-blue-600 font-semibold">Create Account</Text>
              </Pressable>
            </VStack>
          </VStack>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
