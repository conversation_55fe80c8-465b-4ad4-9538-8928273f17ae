import React, { useState } from 'react';
import { Alert, Platform } from 'react-native';
import { router } from 'expo-router';
import {
  Box,
  VStack,
  Text,
  Button,
  ButtonText,
  Input,
  InputField,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  SafeAreaView,
  KeyboardAvoidingView,
  ScrollView,
  Pressable,
} from '@/components/ui';
import { useAuth } from '@/src/stores/authStore';
import { useForm } from '@/src/hooks/useForm';
import { ValidationUtils } from '@/src/utils/validation';
import { LoadingSpinner } from '@/src/components/LoadingSpinner';

interface ForgotPinFormData {
  phoneNumber: string;
}

export default function ForgotPinScreen() {
  const { forgotPin, isLoading, error, clearError } = useAuth();
  const [step, setStep] = useState<'phone' | 'success'>('phone');

  const {
    values,
    errors,
    isValid,
    setValue,
    validate,
    handleSubmit,
  } = useForm<ForgotPinFormData>({
    initialValues: {
      phoneNumber: '',
    },
    validationRules: {
      phoneNumber: ValidationUtils.validatePhoneNumber,
    },
    onSubmit: handleForgotPin,
  });

  async function handleForgotPin(formData: ForgotPinFormData) {
    clearError();
    
    const success = await forgotPin(formData.phoneNumber);
    
    if (success) {
      setStep('success');
    } else {
      Alert.alert('Error', error || 'Failed to send reset code. Please try again.');
    }
  }

  const handleBackToLogin = () => {
    router.push('/(auth)/login');
  };

  if (isLoading) {
    return <LoadingSpinner message="Sending reset code..." overlay />;
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1">
        <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
          <VStack className="flex-1 px-6 py-8" space="lg">
            {/* Header */}
            <VStack className="items-center mt-16 mb-8" space="md">
              <Box className="w-24 h-24 bg-orange-100 rounded-full items-center justify-center">
                <Text className="text-3xl">🔐</Text>
              </Box>
              <Text className="text-2xl font-bold text-gray-800">
                {step === 'phone' ? 'Forgot PIN?' : 'Check Your Phone'}
              </Text>
              <Text className="text-gray-600 text-center">
                {step === 'phone' 
                  ? 'Enter your phone number and we\'ll send you a reset code'
                  : 'We\'ve sent a reset code to your phone number'
                }
              </Text>
            </VStack>

            {step === 'phone' ? (
              <VStack space="lg" className="flex-1">
                <FormControl isInvalid={!!errors.phoneNumber}>
                  <FormControlLabel>
                    <FormControlLabelText>Phone Number</FormControlLabelText>
                  </FormControlLabel>
                  <Input>
                    <InputField
                      placeholder="Enter your phone number"
                      value={values.phoneNumber}
                      onChangeText={(text) => setValue('phoneNumber', text)}
                      keyboardType="phone-pad"
                      autoCapitalize="none"
                      onBlur={() => validate('phoneNumber')}
                    />
                  </Input>
                  <FormControlError>
                    <FormControlErrorText>{errors.phoneNumber}</FormControlErrorText>
                  </FormControlError>
                </FormControl>

                <Button
                  onPress={handleSubmit}
                  isDisabled={isLoading || !isValid}
                  className="mt-4"
                >
                  <ButtonText>{isLoading ? 'Sending...' : 'Send Reset Code'}</ButtonText>
                </Button>

                <Pressable onPress={handleBackToLogin} className="mt-4">
                  <Text className="text-blue-600 text-center">Back to Login</Text>
                </Pressable>
              </VStack>
            ) : (
              <VStack space="lg" className="flex-1">
                <VStack className="items-center" space="md">
                  <Text className="text-gray-700 text-center">
                    Please check your phone for a text message with your reset code.
                    Follow the instructions in the message to reset your PIN.
                  </Text>
                  
                  <Box className="bg-blue-50 p-4 rounded-lg w-full">
                    <Text className="text-blue-800 text-sm text-center">
                      Didn't receive the code? Check your spam folder or try again in a few minutes.
                    </Text>
                  </Box>
                </VStack>

                <Button onPress={handleBackToLogin} className="mt-8">
                  <ButtonText>Back to Login</ButtonText>
                </Button>

                <Pressable onPress={() => setStep('phone')} className="mt-4">
                  <Text className="text-blue-600 text-center">Try Different Number</Text>
                </Pressable>
              </VStack>
            )}
          </VStack>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
