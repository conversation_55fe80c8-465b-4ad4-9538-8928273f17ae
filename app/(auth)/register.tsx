import React, { useState } from 'react';
import { Alert, Platform } from 'react-native';
import { router } from 'expo-router';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonText,
  Input,
  InputField,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  SafeAreaView,
  KeyboardAvoidingView,
  ScrollView,
  Pressable,
  Checkbox,
  CheckboxIndicator,
  CheckboxIcon,
  CheckboxLabel,
  CheckIcon,
} from '@/components/ui';
import { useAuth } from '@/src/stores/authStore';
import { useForm } from '@/src/hooks/useForm';
import { ValidationUtils } from '@/src/utils/validation';
import { LoadingSpinner } from '@/src/components/LoadingSpinner';

interface RegisterFormData {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  pin: string;
  confirmPin: string;
  agreeToTerms: boolean;
}

export default function RegisterScreen() {
  const { register, isLoading, error, clearError } = useAuth();

  const {
    values,
    errors,
    isValid,
    setValue,
    validate,
    handleSubmit,
  } = useForm<RegisterFormData>({
    initialValues: {
      firstName: '',
      lastName: '',
      phoneNumber: '',
      email: '',
      pin: '',
      confirmPin: '',
      agreeToTerms: false,
    },
    validationRules: {
      firstName: ValidationUtils.validateName,
      lastName: ValidationUtils.validateName,
      phoneNumber: ValidationUtils.validatePhoneNumber,
      email: ValidationUtils.validateEmail,
      pin: ValidationUtils.validatePin,
      confirmPin: (value) => ValidationUtils.validateConfirmPin(values.pin, value),
      agreeToTerms: (value) => ValidationUtils.validateRequired(value ? 'true' : '', 'Terms agreement'),
    },
    onSubmit: handleRegister,
  });

  async function handleRegister(formData: RegisterFormData) {
    clearError();
    
    const success = await register(formData);
    
    if (success) {
      Alert.alert(
        'Registration Successful',
        'Your account has been created successfully!',
        [
          {
            text: 'OK',
            onPress: () => router.replace('/(main)/(tabs)/home'),
          },
        ]
      );
    } else {
      Alert.alert('Registration Failed', error || 'Please try again.');
    }
  }

  const handleLogin = () => {
    router.push('/(auth)/login');
  };

  if (isLoading) {
    return <LoadingSpinner message="Creating your account..." overlay />;
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1">
        <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
          <VStack className="flex-1 px-6 py-8" space="lg">
            {/* Header */}
            <VStack className="items-center mt-8 mb-6" space="md">
              <Box className="w-24 h-24 bg-blue-100 rounded-full items-center justify-center">
                <Text className="text-3xl font-bold text-blue-600">OM</Text>
              </Box>
              <Text className="text-2xl font-bold text-gray-800">Create Account</Text>
              <Text className="text-gray-600 text-center">
                Join OneMoney and start your digital wallet journey
              </Text>
            </VStack>

            {/* Form Section */}
            <VStack space="md" className="flex-1">
              <HStack space="md">
                <FormControl isInvalid={!!errors.firstName} className="flex-1">
                  <FormControlLabel>
                    <FormControlLabelText>First Name</FormControlLabelText>
                  </FormControlLabel>
                  <Input>
                    <InputField
                      placeholder="First name"
                      value={values.firstName}
                      onChangeText={(text) => setValue('firstName', text)}
                      autoCapitalize="words"
                      onBlur={() => validate('firstName')}
                    />
                  </Input>
                  <FormControlError>
                    <FormControlErrorText>{errors.firstName}</FormControlErrorText>
                  </FormControlError>
                </FormControl>

                <FormControl isInvalid={!!errors.lastName} className="flex-1">
                  <FormControlLabel>
                    <FormControlLabelText>Last Name</FormControlLabelText>
                  </FormControlLabel>
                  <Input>
                    <InputField
                      placeholder="Last name"
                      value={values.lastName}
                      onChangeText={(text) => setValue('lastName', text)}
                      autoCapitalize="words"
                      onBlur={() => validate('lastName')}
                    />
                  </Input>
                  <FormControlError>
                    <FormControlErrorText>{errors.lastName}</FormControlErrorText>
                  </FormControlError>
                </FormControl>
              </HStack>

              <FormControl isInvalid={!!errors.phoneNumber}>
                <FormControlLabel>
                  <FormControlLabelText>Phone Number</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="Enter your phone number"
                    value={values.phoneNumber}
                    onChangeText={(text) => setValue('phoneNumber', text)}
                    keyboardType="phone-pad"
                    autoCapitalize="none"
                    onBlur={() => validate('phoneNumber')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.phoneNumber}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.email}>
                <FormControlLabel>
                  <FormControlLabelText>Email (Optional)</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="Enter your email"
                    value={values.email}
                    onChangeText={(text) => setValue('email', text)}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    onBlur={() => validate('email')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.email}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.pin}>
                <FormControlLabel>
                  <FormControlLabelText>Create PIN</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="Create a 4-digit PIN"
                    value={values.pin}
                    onChangeText={(text) => setValue('pin', text)}
                    keyboardType="numeric"
                    secureTextEntry
                    maxLength={4}
                    onBlur={() => validate('pin')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.pin}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.confirmPin}>
                <FormControlLabel>
                  <FormControlLabelText>Confirm PIN</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="Confirm your PIN"
                    value={values.confirmPin}
                    onChangeText={(text) => setValue('confirmPin', text)}
                    keyboardType="numeric"
                    secureTextEntry
                    maxLength={4}
                    onBlur={() => validate('confirmPin')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.confirmPin}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.agreeToTerms}>
                <Checkbox
                  value={values.agreeToTerms}
                  onChange={(checked) => setValue('agreeToTerms', checked)}
                  className="mt-4"
                >
                  <CheckboxIndicator>
                    <CheckboxIcon as={CheckIcon} />
                  </CheckboxIndicator>
                  <CheckboxLabel className="ml-2">
                    <Text className="text-sm text-gray-700">
                      I agree to the{' '}
                      <Text className="text-blue-600 underline">Terms of Service</Text>
                      {' '}and{' '}
                      <Text className="text-blue-600 underline">Privacy Policy</Text>
                    </Text>
                  </CheckboxLabel>
                </Checkbox>
                <FormControlError>
                  <FormControlErrorText>{errors.agreeToTerms}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <Button
                onPress={handleSubmit}
                isDisabled={isLoading || !isValid}
                className="mt-6"
              >
                <ButtonText>{isLoading ? 'Creating Account...' : 'Create Account'}</ButtonText>
              </Button>
            </VStack>

            {/* Login Section */}
            <VStack className="items-center mt-6" space="md">
              <Text className="text-gray-600">Already have an account?</Text>
              <Pressable onPress={handleLogin}>
                <Text className="text-blue-600 font-semibold">Sign In</Text>
              </Pressable>
            </VStack>
          </VStack>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
