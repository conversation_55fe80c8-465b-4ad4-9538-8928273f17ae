import React, { useState } from 'react';
import { Alert, Platform } from 'react-native';
import { router } from 'expo-router';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonText,
  Input,
  InputField,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  SafeAreaView,
  KeyboardAvoidingView,
  ScrollView,
  Pressable,
  Card,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  ChevronDownIcon,
  Badge,
  BadgeText,
} from '@/components/ui';
import { usePayment } from '@/src/stores/walletStore';
import { useWallet } from '@/src/stores/walletStore';
import { useForm } from '@/src/hooks/useForm';
import { ValidationUtils } from '@/src/utils/validation';
import { FormattingUtils } from '@/src/utils/formatting';
import { LoadingSpinner } from '@/src/components/LoadingSpinner';

interface AirtimeFormData {
  provider: string;
  phoneNumber: string;
  type: 'airtime' | 'data';
  amount: string;
  bundle?: string;
}

export default function AirtimeScreen() {
  const { makePayment, isProcessing, error } = usePayment();
  const { balance } = useWallet();

  const providers = [
    { id: 'econet', name: 'Econet', color: 'bg-red-100', textColor: 'text-red-600' },
    { id: 'netone', name: 'NetOne', color: 'bg-blue-100', textColor: 'text-blue-600' },
    { id: 'telecel', name: 'Telecel', color: 'bg-green-100', textColor: 'text-green-600' },
  ];

  const dataBundles = {
    econet: [
      { id: 'daily_150mb', name: 'Daily 150MB', amount: '1.00', validity: '24 hours' },
      { id: 'weekly_1gb', name: 'Weekly 1GB', amount: '5.00', validity: '7 days' },
      { id: 'monthly_5gb', name: 'Monthly 5GB', amount: '20.00', validity: '30 days' },
    ],
    netone: [
      { id: 'daily_100mb', name: 'Daily 100MB', amount: '0.80', validity: '24 hours' },
      { id: 'weekly_750mb', name: 'Weekly 750MB', amount: '4.00', validity: '7 days' },
      { id: 'monthly_3gb', name: 'Monthly 3GB', amount: '15.00', validity: '30 days' },
    ],
    telecel: [
      { id: 'daily_200mb', name: 'Daily 200MB', amount: '1.20', validity: '24 hours' },
      { id: 'weekly_1_5gb', name: 'Weekly 1.5GB', amount: '6.00', validity: '7 days' },
      { id: 'monthly_7gb', name: 'Monthly 7GB', amount: '25.00', validity: '30 days' },
    ],
  };

  const {
    values,
    errors,
    isValid,
    setValue,
    validate,
    handleSubmit,
  } = useForm<AirtimeFormData>({
    initialValues: {
      provider: '',
      phoneNumber: '',
      type: 'airtime',
      amount: '',
      bundle: '',
    },
    validationRules: {
      provider: (value) => ValidationUtils.validateRequired(value, 'Provider'),
      phoneNumber: ValidationUtils.validatePhoneNumber,
      type: (value) => ValidationUtils.validateRequired(value, 'Type'),
      amount: ValidationUtils.validateAmount,
      bundle: (value) => values.type === 'data' ? ValidationUtils.validateRequired(value, 'Data bundle') : { isValid: true },
    },
    onSubmit: handleAirtimePurchase,
  });

  async function handleAirtimePurchase(formData: AirtimeFormData) {
    // Check if user has sufficient balance
    if (balance && parseFloat(formData.amount) > parseFloat(balance.balance)) {
      Alert.alert('Insufficient Funds', 'You do not have enough balance for this purchase.');
      return;
    }

    const success = await makePayment({
      ...formData,
      type: 'airtime',
      currency: balance?.currType || 'USD',
      description: `${formData.type} purchase for ${formData.phoneNumber}`,
    });

    if (success) {
      Alert.alert(
        'Purchase Successful',
        `${formData.type === 'airtime' ? 'Airtime' : 'Data bundle'} has been sent to ${formData.phoneNumber}!`,
        [
          {
            text: 'OK',
            onPress: () => router.push('/(main)/(tabs)/home'),
          },
        ]
      );
    } else {
      Alert.alert('Purchase Failed', error || 'Please try again.');
    }
  }

  const handleGoBack = () => {
    router.back();
  };

  const selectedProvider = providers.find(p => p.id === values.provider);
  const availableBundles = values.provider ? dataBundles[values.provider as keyof typeof dataBundles] || [] : [];
  const selectedBundle = availableBundles.find(b => b.id === values.bundle);
  const hasInsufficientFunds = values.amount && balance ? parseFloat(values.amount) > parseFloat(balance.balance) : false;

  // Auto-set amount when bundle is selected
  React.useEffect(() => {
    if (selectedBundle && values.type === 'data') {
      setValue('amount', selectedBundle.amount);
    }
  }, [selectedBundle, values.type]);

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1">
        {/* Header */}
        <HStack className="justify-between items-center p-4 bg-white border-b border-gray-200">
          <Pressable onPress={handleGoBack}>
            <Box className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center">
              <Text className="text-gray-600 text-lg">←</Text>
            </Box>
          </Pressable>
          
          <Text className="text-lg font-semibold text-gray-800">Buy Airtime & Data</Text>
          
          <Box className="w-10 h-10" />
        </HStack>

        <ScrollView className="flex-1">
          <VStack className="p-6" space="lg">
            {/* Balance Display */}
            <Card className="bg-white">
              <VStack className="p-4" space="sm">
                <Text className="text-gray-600 text-sm">Available Balance</Text>
                <Text className="text-2xl font-bold text-gray-800">
                  {balance ? FormattingUtils.formatCurrency(balance.balance, balance.currType) : '---'}
                </Text>
              </VStack>
            </Card>

            {/* Type Selection */}
            <VStack space="md">
              <Text className="text-lg font-semibold text-gray-800">Select Type</Text>
              <HStack space="md">
                <Pressable
                  onPress={() => setValue('type', 'airtime')}
                  className={`flex-1 p-4 rounded-lg border-2 ${
                    values.type === 'airtime' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 bg-white'
                  }`}
                >
                  <VStack className="items-center" space="sm">
                    <Text className="text-2xl">📞</Text>
                    <Text className={`font-medium ${
                      values.type === 'airtime' ? 'text-blue-600' : 'text-gray-600'
                    }`}>
                      Airtime
                    </Text>
                  </VStack>
                </Pressable>

                <Pressable
                  onPress={() => setValue('type', 'data')}
                  className={`flex-1 p-4 rounded-lg border-2 ${
                    values.type === 'data' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 bg-white'
                  }`}
                >
                  <VStack className="items-center" space="sm">
                    <Text className="text-2xl">📶</Text>
                    <Text className={`font-medium ${
                      values.type === 'data' ? 'text-blue-600' : 'text-gray-600'
                    }`}>
                      Data
                    </Text>
                  </VStack>
                </Pressable>
              </HStack>
            </VStack>

            {/* Provider Selection */}
            <VStack space="md">
              <Text className="text-lg font-semibold text-gray-800">Select Provider</Text>
              <VStack space="sm">
                {providers.map((provider) => (
                  <Pressable
                    key={provider.id}
                    onPress={() => setValue('provider', provider.id)}
                  >
                    <Card className={`${
                      values.provider === provider.id ? 'border-blue-500 bg-blue-50' : 'bg-white'
                    }`}>
                      <HStack className="p-4 items-center justify-between">
                        <HStack className="items-center" space="md">
                          <Box className={`w-12 h-12 ${provider.color} rounded-full items-center justify-center`}>
                            <Text className={`font-bold ${provider.textColor}`}>
                              {provider.name.charAt(0)}
                            </Text>
                          </Box>
                          <Text className="font-medium text-gray-800">{provider.name}</Text>
                        </HStack>
                        {values.provider === provider.id && (
                          <Text className="text-blue-600 text-xl">✓</Text>
                        )}
                      </HStack>
                    </Card>
                  </Pressable>
                ))}
              </VStack>
            </VStack>

            {/* Form Fields */}
            <VStack space="md">
              <FormControl isInvalid={!!errors.phoneNumber}>
                <FormControlLabel>
                  <FormControlLabelText>Phone Number</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="Enter phone number"
                    value={values.phoneNumber}
                    onChangeText={(text) => setValue('phoneNumber', text)}
                    keyboardType="phone-pad"
                    onBlur={() => validate('phoneNumber')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.phoneNumber}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              {values.type === 'data' && values.provider && (
                <FormControl isInvalid={!!errors.bundle}>
                  <FormControlLabel>
                    <FormControlLabelText>Data Bundle</FormControlLabelText>
                  </FormControlLabel>
                  <VStack space="sm">
                    {availableBundles.map((bundle) => (
                      <Pressable
                        key={bundle.id}
                        onPress={() => setValue('bundle', bundle.id)}
                      >
                        <Card className={`${
                          values.bundle === bundle.id ? 'border-blue-500 bg-blue-50' : 'bg-white'
                        }`}>
                          <HStack className="p-4 items-center justify-between">
                            <VStack>
                              <Text className="font-medium text-gray-800">{bundle.name}</Text>
                              <Text className="text-sm text-gray-600">Valid for {bundle.validity}</Text>
                            </VStack>
                            <VStack className="items-end">
                              <Text className="font-bold text-lg">
                                {FormattingUtils.formatCurrency(bundle.amount, balance?.currType || 'USD')}
                              </Text>
                              {values.bundle === bundle.id && (
                                <Text className="text-blue-600 text-xl">✓</Text>
                              )}
                            </VStack>
                          </HStack>
                        </Card>
                      </Pressable>
                    ))}
                  </VStack>
                  <FormControlError>
                    <FormControlErrorText>{errors.bundle}</FormControlErrorText>
                  </FormControlError>
                </FormControl>
              )}

              {values.type === 'airtime' && (
                <FormControl isInvalid={!!errors.amount || hasInsufficientFunds}>
                  <FormControlLabel>
                    <FormControlLabelText>Amount</FormControlLabelText>
                  </FormControlLabel>
                  <Input>
                    <InputField
                      placeholder="0.00"
                      value={values.amount}
                      onChangeText={(text) => setValue('amount', text)}
                      keyboardType="decimal-pad"
                      onBlur={() => validate('amount')}
                    />
                  </Input>
                  <FormControlError>
                    <FormControlErrorText>
                      {errors.amount || (hasInsufficientFunds ? 'Insufficient funds' : '')}
                    </FormControlErrorText>
                  </FormControlError>
                </FormControl>
              )}
            </VStack>

            {/* Purchase Summary */}
            {values.amount && values.provider && values.phoneNumber && (
              <Card className="bg-gray-50">
                <VStack className="p-4" space="sm">
                  <Text className="font-semibold text-gray-800">Purchase Summary</Text>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Type</Text>
                    <Text className="font-medium capitalize">{values.type}</Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Provider</Text>
                    <Text className="font-medium">{selectedProvider?.name}</Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Phone Number</Text>
                    <Text className="font-medium">{values.phoneNumber}</Text>
                  </HStack>
                  {selectedBundle && (
                    <HStack className="justify-between">
                      <Text className="text-gray-600">Bundle</Text>
                      <Text className="font-medium">{selectedBundle.name}</Text>
                    </HStack>
                  )}
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Amount</Text>
                    <Text className="font-medium">
                      {FormattingUtils.formatCurrency(values.amount, balance?.currType || 'USD')}
                    </Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Fee</Text>
                    <Text className="font-medium">Free</Text>
                  </HStack>
                  <Divider />
                  <HStack className="justify-between">
                    <Text className="font-semibold text-gray-800">Total</Text>
                    <Text className="font-bold text-lg">
                      {FormattingUtils.formatCurrency(values.amount, balance?.currType || 'USD')}
                    </Text>
                  </HStack>
                </VStack>
              </Card>
            )}

            {/* Submit Button */}
            <Button
              onPress={handleSubmit}
              isDisabled={isProcessing || !isValid || hasInsufficientFunds}
              className="mt-4"
            >
              <ButtonText>
                {isProcessing ? 'Processing...' : `Buy ${values.type === 'airtime' ? 'Airtime' : 'Data'}`}
              </ButtonText>
            </Button>
          </VStack>
        </ScrollView>
      </KeyboardAvoidingView>

      {isProcessing && (
        <LoadingSpinner message={`Processing ${values.type} purchase...`} overlay />
      )}
    </SafeAreaView>
  );
}
