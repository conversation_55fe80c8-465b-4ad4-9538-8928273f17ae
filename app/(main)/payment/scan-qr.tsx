import React, { useState, useEffect } from 'react';
import { Alert, Platform } from 'react-native';
import { router } from 'expo-router';
import { Camera, CameraView, useCameraPermissions } from 'expo-camera';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonText,
  SafeAreaView,
  Pressable,
} from '@/components/ui';
import { usePayment } from '@/src/stores/walletStore';
import { LoadingSpinner } from '@/src/components/LoadingSpinner';

export default function ScanQRScreen() {
  const [permission, requestPermission] = useCameraPermissions();
  const [scanned, setScanned] = useState(false);
  const [flashOn, setFlashOn] = useState(false);
  const { scanQR, isProcessing, error, setError } = usePayment();

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error);
      setError(null);
    }
  }, [error]);

  const handleBarCodeScanned = async ({ data }: { data: string }) => {
    if (scanned) return;
    
    setScanned(true);
    
    try {
      const result = await scanQR(data);
      
      if (result) {
        // Navigate to payment confirmation with QR data
        router.push({
          pathname: '/(main)/payment/make-payment',
          params: { qrData: JSON.stringify(result) },
        });
      } else {
        Alert.alert(
          'Invalid QR Code',
          'This QR code is not valid for payments.',
          [{ text: 'Try Again', onPress: () => setScanned(false) }]
        );
      }
    } catch (error) {
      Alert.alert(
        'Scan Error',
        'Failed to process QR code. Please try again.',
        [{ text: 'Try Again', onPress: () => setScanned(false) }]
      );
    }
  };

  const toggleFlash = () => {
    setFlashOn(!flashOn);
  };

  const handleManualEntry = () => {
    router.push('/(main)/payment/make-payment');
  };

  const handleGoBack = () => {
    router.back();
  };

  if (!permission) {
    return <LoadingSpinner message="Loading camera..." />;
  }

  if (!permission.granted) {
    return (
      <SafeAreaView className="flex-1 bg-black">
        <VStack className="flex-1 items-center justify-center px-6" space="lg">
          <Box className="w-20 h-20 bg-gray-800 rounded-full items-center justify-center">
            <Text className="text-white text-3xl">📷</Text>
          </Box>
          
          <VStack className="items-center" space="sm">
            <Text className="text-white text-xl font-bold text-center">
              Camera Permission Required
            </Text>
            <Text className="text-gray-300 text-center">
              We need access to your camera to scan QR codes for payments.
            </Text>
          </VStack>

          <VStack className="w-full" space="md">
            <Button onPress={requestPermission}>
              <ButtonText>Grant Camera Permission</ButtonText>
            </Button>
            
            <Button variant="outline" onPress={handleManualEntry}>
              <ButtonText>Enter Payment Details Manually</ButtonText>
            </Button>
          </VStack>
        </VStack>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-black">
      <VStack className="flex-1">
        {/* Header */}
        <HStack className="justify-between items-center p-4 bg-black/50">
          <Pressable onPress={handleGoBack}>
            <Box className="w-10 h-10 bg-white/20 rounded-full items-center justify-center">
              <Text className="text-white text-lg">←</Text>
            </Box>
          </Pressable>
          
          <Text className="text-white text-lg font-semibold">Scan QR Code</Text>
          
          <Pressable onPress={toggleFlash}>
            <Box className="w-10 h-10 bg-white/20 rounded-full items-center justify-center">
              <Text className="text-white text-lg">{flashOn ? '🔦' : '💡'}</Text>
            </Box>
          </Pressable>
        </HStack>

        {/* Camera View */}
        <Box className="flex-1 relative">
          <CameraView
            style={{ flex: 1 }}
            facing="back"
            flash={flashOn ? 'on' : 'off'}
            onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
            barcodeScannerSettings={{
              barcodeTypes: ['qr'],
            }}
          />
          
          {/* Scanning Overlay */}
          <Box className="absolute inset-0 items-center justify-center">
            <Box className="w-64 h-64 border-2 border-white rounded-lg relative">
              {/* Corner indicators */}
              <Box className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-blue-500 rounded-tl-lg" />
              <Box className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-blue-500 rounded-tr-lg" />
              <Box className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-blue-500 rounded-bl-lg" />
              <Box className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-blue-500 rounded-br-lg" />
            </Box>
          </Box>
          
          {/* Instructions */}
          <Box className="absolute bottom-32 left-0 right-0 items-center px-6">
            <VStack className="items-center" space="sm">
              <Text className="text-white text-lg font-semibold text-center">
                {scanned ? 'Processing...' : 'Point camera at QR code'}
              </Text>
              <Text className="text-gray-300 text-sm text-center">
                {scanned 
                  ? 'Please wait while we process the QR code'
                  : 'Make sure the QR code is clearly visible within the frame'
                }
              </Text>
            </VStack>
          </Box>
        </Box>

        {/* Bottom Actions */}
        <Box className="p-6 bg-black/50">
          <VStack space="md">
            <Button 
              variant="outline" 
              onPress={handleManualEntry}
              isDisabled={scanned || isProcessing}
            >
              <ButtonText className="text-white">Enter Details Manually</ButtonText>
            </Button>
            
            {scanned && (
              <Button 
                variant="outline" 
                onPress={() => setScanned(false)}
                isDisabled={isProcessing}
              >
                <ButtonText className="text-white">Scan Again</ButtonText>
              </Button>
            )}
          </VStack>
        </Box>
      </VStack>
      
      {isProcessing && (
        <LoadingSpinner message="Processing QR code..." overlay />
      )}
    </SafeAreaView>
  );
}
