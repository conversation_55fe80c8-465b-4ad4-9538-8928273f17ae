import React, { useState, useEffect } from 'react';
import { Alert, Platform } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonText,
  Input,
  InputField,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  SafeAreaView,
  KeyboardAvoidingView,
  ScrollView,
  Pressable,
  Card,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  ChevronDownIcon,
} from '@/components/ui';
import { usePayment } from '@/src/stores/walletStore';
import { useWallet } from '@/src/stores/walletStore';
import { useForm } from '@/src/hooks/useForm';
import { ValidationUtils } from '@/src/utils/validation';
import { FormattingUtils } from '@/src/utils/formatting';
import { LoadingSpinner } from '@/src/components/LoadingSpinner';

interface PaymentFormData {
  recipient: string;
  amount: string;
  description: string;
  paymentMethod: string;
}

export default function MakePaymentScreen() {
  const params = useLocalSearchParams();
  const { makePayment, isProcessing, error, setError } = usePayment();
  const { balance, paymentMethods } = useWallet();
  const [qrData, setQrData] = useState<any>(null);

  useEffect(() => {
    if (params.qrData) {
      try {
        const data = JSON.parse(params.qrData as string);
        setQrData(data);
      } catch (error) {
        console.error('Failed to parse QR data:', error);
      }
    }
  }, [params.qrData]);

  const {
    values,
    errors,
    isValid,
    setValue,
    validate,
    handleSubmit,
  } = useForm<PaymentFormData>({
    initialValues: {
      recipient: qrData?.recipient || '',
      amount: qrData?.amount || '',
      description: qrData?.description || '',
      paymentMethod: paymentMethods[0]?.id || 'wallet',
    },
    validationRules: {
      recipient: (value) => ValidationUtils.validateRequired(value, 'Recipient'),
      amount: ValidationUtils.validateAmount,
      description: (value) => ValidationUtils.validateRequired(value, 'Description'),
      paymentMethod: (value) => ValidationUtils.validateRequired(value, 'Payment method'),
    },
    onSubmit: handlePayment,
  });

  async function handlePayment(formData: PaymentFormData) {
    // Check if user has sufficient balance
    if (balance && parseFloat(formData.amount) > parseFloat(balance.balance)) {
      Alert.alert('Insufficient Funds', 'You do not have enough balance for this payment.');
      return;
    }

    const success = await makePayment({
      ...formData,
      type: 'payment',
      currency: balance?.currType || 'USD',
    });

    if (success) {
      Alert.alert(
        'Payment Successful',
        `Payment of ${FormattingUtils.formatCurrency(formData.amount, balance?.currType || 'USD')} sent successfully!`,
        [
          {
            text: 'OK',
            onPress: () => router.push('/(main)/(tabs)/home'),
          },
        ]
      );
    } else {
      Alert.alert('Payment Failed', error || 'Please try again.');
    }
  }

  const handleScanQR = () => {
    router.push('/(main)/payment/scan-qr');
  };

  const handleGoBack = () => {
    router.back();
  };

  const availableBalance = balance ? parseFloat(balance.balance) : 0;
  const paymentAmount = values.amount ? parseFloat(values.amount) : 0;
  const hasInsufficientFunds = paymentAmount > availableBalance;

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1">
        {/* Header */}
        <HStack className="justify-between items-center p-4 bg-white border-b border-gray-200">
          <Pressable onPress={handleGoBack}>
            <Box className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center">
              <Text className="text-gray-600 text-lg">←</Text>
            </Box>
          </Pressable>
          
          <Text className="text-lg font-semibold text-gray-800">Make Payment</Text>
          
          <Pressable onPress={handleScanQR}>
            <Box className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center">
              <Text className="text-blue-600 text-lg">📱</Text>
            </Box>
          </Pressable>
        </HStack>

        <ScrollView className="flex-1">
          <VStack className="p-6" space="lg">
            {/* QR Data Info */}
            {qrData && (
              <Card className="bg-blue-50 border-blue-200">
                <VStack className="p-4" space="sm">
                  <HStack className="items-center" space="sm">
                    <Text className="text-blue-600 text-lg">📱</Text>
                    <Text className="text-blue-800 font-semibold">QR Payment</Text>
                  </HStack>
                  <Text className="text-blue-700 text-sm">
                    Payment details loaded from QR code
                  </Text>
                </VStack>
              </Card>
            )}

            {/* Balance Display */}
            <Card className="bg-white">
              <VStack className="p-4" space="sm">
                <Text className="text-gray-600 text-sm">Available Balance</Text>
                <Text className="text-2xl font-bold text-gray-800">
                  {balance ? FormattingUtils.formatCurrency(balance.balance, balance.currType) : '---'}
                </Text>
              </VStack>
            </Card>

            {/* Payment Form */}
            <VStack space="md">
              <FormControl isInvalid={!!errors.recipient}>
                <FormControlLabel>
                  <FormControlLabelText>Recipient</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="Enter recipient name or phone number"
                    value={values.recipient}
                    onChangeText={(text) => setValue('recipient', text)}
                    onBlur={() => validate('recipient')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.recipient}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.amount || hasInsufficientFunds}>
                <FormControlLabel>
                  <FormControlLabelText>Amount</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="0.00"
                    value={values.amount}
                    onChangeText={(text) => setValue('amount', text)}
                    keyboardType="decimal-pad"
                    onBlur={() => validate('amount')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>
                    {errors.amount || (hasInsufficientFunds ? 'Insufficient funds' : '')}
                  </FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.description}>
                <FormControlLabel>
                  <FormControlLabelText>Description</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="What's this payment for?"
                    value={values.description}
                    onChangeText={(text) => setValue('description', text)}
                    onBlur={() => validate('description')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.description}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.paymentMethod}>
                <FormControlLabel>
                  <FormControlLabelText>Payment Method</FormControlLabelText>
                </FormControlLabel>
                <Select
                  selectedValue={values.paymentMethod}
                  onValueChange={(value) => setValue('paymentMethod', value)}
                >
                  <SelectTrigger>
                    <SelectInput placeholder="Select payment method" />
                    <SelectIcon as={ChevronDownIcon} />
                  </SelectTrigger>
                  <SelectPortal>
                    <SelectBackdrop />
                    <SelectContent>
                      <SelectDragIndicatorWrapper>
                        <SelectDragIndicator />
                      </SelectDragIndicatorWrapper>
                      <SelectItem label="Wallet Balance" value="wallet" />
                      {paymentMethods.map((method) => (
                        <SelectItem
                          key={method.id}
                          label={method.name}
                          value={method.id}
                        />
                      ))}
                    </SelectContent>
                  </SelectPortal>
                </Select>
                <FormControlError>
                  <FormControlErrorText>{errors.paymentMethod}</FormControlErrorText>
                </FormControlError>
              </FormControl>
            </VStack>

            {/* Payment Summary */}
            {values.amount && (
              <Card className="bg-gray-50">
                <VStack className="p-4" space="sm">
                  <Text className="font-semibold text-gray-800">Payment Summary</Text>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Amount</Text>
                    <Text className="font-medium">
                      {FormattingUtils.formatCurrency(values.amount, balance?.currType || 'USD')}
                    </Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Fee</Text>
                    <Text className="font-medium">Free</Text>
                  </HStack>
                  <Divider />
                  <HStack className="justify-between">
                    <Text className="font-semibold text-gray-800">Total</Text>
                    <Text className="font-bold text-lg">
                      {FormattingUtils.formatCurrency(values.amount, balance?.currType || 'USD')}
                    </Text>
                  </HStack>
                </VStack>
              </Card>
            )}

            {/* Submit Button */}
            <Button
              onPress={handleSubmit}
              isDisabled={isProcessing || !isValid || hasInsufficientFunds}
              className="mt-4"
            >
              <ButtonText>
                {isProcessing ? 'Processing...' : 'Send Payment'}
              </ButtonText>
            </Button>
          </VStack>
        </ScrollView>
      </KeyboardAvoidingView>

      {isProcessing && (
        <LoadingSpinner message="Processing payment..." overlay />
      )}
    </SafeAreaView>
  );
}
