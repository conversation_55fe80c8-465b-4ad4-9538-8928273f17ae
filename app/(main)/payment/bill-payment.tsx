import React, { useState } from 'react';
import { Alert, Platform } from 'react-native';
import { router } from 'expo-router';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonText,
  Input,
  InputField,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  SafeAreaView,
  KeyboardAvoidingView,
  ScrollView,
  Pressable,
  Card,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  ChevronDownIcon,
} from '@/components/ui';
import { usePayment } from '@/src/stores/walletStore';
import { useWallet } from '@/src/stores/walletStore';
import { useForm } from '@/src/hooks/useForm';
import { ValidationUtils } from '@/src/utils/validation';
import { FormattingUtils } from '@/src/utils/formatting';
import { LoadingSpinner } from '@/src/components/LoadingSpinner';
import { BILL_TYPES } from '@/src/constants';

interface BillPaymentFormData {
  billType: string;
  provider: string;
  accountNumber: string;
  amount: string;
  customerName: string;
}

export default function BillPaymentScreen() {
  const { makePayment, isProcessing, error } = usePayment();
  const { balance } = useWallet();

  const billProviders = {
    electricity: ['ZESA', 'City Council', 'Rural Electrification'],
    water: ['City Council', 'Zimbabwe National Water Authority'],
    internet: ['Econet', 'NetOne', 'TelOne', 'Liquid Telecom'],
    school: ['University of Zimbabwe', 'NUST', 'Midlands State University'],
    municipal: ['Harare City Council', 'Bulawayo City Council'],
  };

  const {
    values,
    errors,
    isValid,
    setValue,
    validate,
    handleSubmit,
  } = useForm<BillPaymentFormData>({
    initialValues: {
      billType: '',
      provider: '',
      accountNumber: '',
      amount: '',
      customerName: '',
    },
    validationRules: {
      billType: (value) => ValidationUtils.validateRequired(value, 'Bill type'),
      provider: (value) => ValidationUtils.validateRequired(value, 'Provider'),
      accountNumber: (value) => ValidationUtils.validateRequired(value, 'Account number'),
      amount: ValidationUtils.validateAmount,
      customerName: (value) => ValidationUtils.validateRequired(value, 'Customer name'),
    },
    onSubmit: handleBillPayment,
  });

  async function handleBillPayment(formData: BillPaymentFormData) {
    // Check if user has sufficient balance
    if (balance && parseFloat(formData.amount) > parseFloat(balance.balance)) {
      Alert.alert('Insufficient Funds', 'You do not have enough balance for this payment.');
      return;
    }

    const success = await makePayment({
      ...formData,
      type: 'bill_payment',
      currency: balance?.currType || 'USD',
      description: `${formData.billType} bill payment to ${formData.provider}`,
    });

    if (success) {
      Alert.alert(
        'Bill Payment Successful',
        `Your ${formData.billType} bill has been paid successfully!`,
        [
          {
            text: 'OK',
            onPress: () => router.push('/(main)/(tabs)/home'),
          },
        ]
      );
    } else {
      Alert.alert('Payment Failed', error || 'Please try again.');
    }
  }

  const handleGoBack = () => {
    router.back();
  };

  const availableProviders = values.billType ? billProviders[values.billType as keyof typeof billProviders] || [] : [];
  const hasInsufficientFunds = values.amount && balance ? parseFloat(values.amount) > parseFloat(balance.balance) : false;

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1">
        {/* Header */}
        <HStack className="justify-between items-center p-4 bg-white border-b border-gray-200">
          <Pressable onPress={handleGoBack}>
            <Box className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center">
              <Text className="text-gray-600 text-lg">←</Text>
            </Box>
          </Pressable>
          
          <Text className="text-lg font-semibold text-gray-800">Pay Bills</Text>
          
          <Box className="w-10 h-10" />
        </HStack>

        <ScrollView className="flex-1">
          <VStack className="p-6" space="lg">
            {/* Balance Display */}
            <Card className="bg-white">
              <VStack className="p-4" space="sm">
                <Text className="text-gray-600 text-sm">Available Balance</Text>
                <Text className="text-2xl font-bold text-gray-800">
                  {balance ? FormattingUtils.formatCurrency(balance.balance, balance.currType) : '---'}
                </Text>
              </VStack>
            </Card>

            {/* Bill Payment Form */}
            <VStack space="md">
              <FormControl isInvalid={!!errors.billType}>
                <FormControlLabel>
                  <FormControlLabelText>Bill Type</FormControlLabelText>
                </FormControlLabel>
                <Select
                  selectedValue={values.billType}
                  onValueChange={(value) => {
                    setValue('billType', value);
                    setValue('provider', ''); // Reset provider when bill type changes
                  }}
                >
                  <SelectTrigger>
                    <SelectInput placeholder="Select bill type" />
                    <SelectIcon as={ChevronDownIcon} />
                  </SelectTrigger>
                  <SelectPortal>
                    <SelectBackdrop />
                    <SelectContent>
                      <SelectDragIndicatorWrapper>
                        <SelectDragIndicator />
                      </SelectDragIndicatorWrapper>
                      <SelectItem label="Electricity" value="electricity" />
                      <SelectItem label="Water" value="water" />
                      <SelectItem label="Internet" value="internet" />
                      <SelectItem label="School Fees" value="school" />
                      <SelectItem label="Municipal Services" value="municipal" />
                    </SelectContent>
                  </SelectPortal>
                </Select>
                <FormControlError>
                  <FormControlErrorText>{errors.billType}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              {values.billType && (
                <FormControl isInvalid={!!errors.provider}>
                  <FormControlLabel>
                    <FormControlLabelText>Provider</FormControlLabelText>
                  </FormControlLabel>
                  <Select
                    selectedValue={values.provider}
                    onValueChange={(value) => setValue('provider', value)}
                  >
                    <SelectTrigger>
                      <SelectInput placeholder="Select provider" />
                      <SelectIcon as={ChevronDownIcon} />
                    </SelectTrigger>
                    <SelectPortal>
                      <SelectBackdrop />
                      <SelectContent>
                        <SelectDragIndicatorWrapper>
                          <SelectDragIndicator />
                        </SelectDragIndicatorWrapper>
                        {availableProviders.map((provider) => (
                          <SelectItem
                            key={provider}
                            label={provider}
                            value={provider}
                          />
                        ))}
                      </SelectContent>
                    </SelectPortal>
                  </Select>
                  <FormControlError>
                    <FormControlErrorText>{errors.provider}</FormControlErrorText>
                  </FormControlError>
                </FormControl>
              )}

              <FormControl isInvalid={!!errors.accountNumber}>
                <FormControlLabel>
                  <FormControlLabelText>Account Number</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="Enter your account number"
                    value={values.accountNumber}
                    onChangeText={(text) => setValue('accountNumber', text)}
                    onBlur={() => validate('accountNumber')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.accountNumber}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.customerName}>
                <FormControlLabel>
                  <FormControlLabelText>Customer Name</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="Enter customer name"
                    value={values.customerName}
                    onChangeText={(text) => setValue('customerName', text)}
                    onBlur={() => validate('customerName')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.customerName}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.amount || hasInsufficientFunds}>
                <FormControlLabel>
                  <FormControlLabelText>Amount</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="0.00"
                    value={values.amount}
                    onChangeText={(text) => setValue('amount', text)}
                    keyboardType="decimal-pad"
                    onBlur={() => validate('amount')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>
                    {errors.amount || (hasInsufficientFunds ? 'Insufficient funds' : '')}
                  </FormControlErrorText>
                </FormControlError>
              </FormControl>
            </VStack>

            {/* Payment Summary */}
            {values.amount && values.provider && (
              <Card className="bg-gray-50">
                <VStack className="p-4" space="sm">
                  <Text className="font-semibold text-gray-800">Payment Summary</Text>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Bill Type</Text>
                    <Text className="font-medium capitalize">{values.billType}</Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Provider</Text>
                    <Text className="font-medium">{values.provider}</Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Account</Text>
                    <Text className="font-medium">{values.accountNumber}</Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Amount</Text>
                    <Text className="font-medium">
                      {FormattingUtils.formatCurrency(values.amount, balance?.currType || 'USD')}
                    </Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Fee</Text>
                    <Text className="font-medium">Free</Text>
                  </HStack>
                  <Divider />
                  <HStack className="justify-between">
                    <Text className="font-semibold text-gray-800">Total</Text>
                    <Text className="font-bold text-lg">
                      {FormattingUtils.formatCurrency(values.amount, balance?.currType || 'USD')}
                    </Text>
                  </HStack>
                </VStack>
              </Card>
            )}

            {/* Submit Button */}
            <Button
              onPress={handleSubmit}
              isDisabled={isProcessing || !isValid || hasInsufficientFunds}
              className="mt-4"
            >
              <ButtonText>
                {isProcessing ? 'Processing...' : 'Pay Bill'}
              </ButtonText>
            </Button>
          </VStack>
        </ScrollView>
      </KeyboardAvoidingView>

      {isProcessing && (
        <LoadingSpinner message="Processing bill payment..." overlay />
      )}
    </SafeAreaView>
  );
}
