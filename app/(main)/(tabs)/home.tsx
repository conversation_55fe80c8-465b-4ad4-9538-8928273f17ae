import React, { useEffect, useState } from 'react';
import { RefreshControl, Alert } from 'react-native';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonText,
  Card,
  SafeAreaView,
  ScrollView,
  Pressable,
  Divider,
  Badge,
  BadgeText,
} from '@/components/ui';
import { useWallet } from '@/src/stores/walletStore';
import { useAuth } from '@/src/stores/authStore';
import { FormattingUtils } from '@/src/utils/formatting';
import { LoadingSpinner } from '@/src/components/LoadingSpinner';
import { EmptyState } from '@/src/components/EmptyState';
import { router } from 'expo-router';

export default function HomeScreen() {
  const { user } = useAuth();
  const { balance, transactions, isLoading, fetchBalance, fetchTransactions, refreshWallet } = useWallet();
  const [balanceVisible, setBalanceVisible] = useState(true);
  const [greeting, setGreeting] = useState('');

  useEffect(() => {
    refreshWallet();
    setGreeting(getGreeting());
  }, []);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  const handleRefresh = async () => {
    await refreshWallet();
  };

  const quickActions = [
    {
      id: 'send',
      title: 'Send Money',
      icon: '💸',
      color: 'bg-blue-100',
      route: '/(main)/transfer/send-money',
    },
    {
      id: 'receive',
      title: 'Receive',
      icon: '💰',
      color: 'bg-green-100',
      route: '/(main)/payment/make-payment',
    },
    {
      id: 'topup',
      title: 'Top Up',
      icon: '⬆️',
      color: 'bg-purple-100',
      route: '/(main)/topup',
    },
    {
      id: 'scan',
      title: 'Scan QR',
      icon: '📱',
      color: 'bg-orange-100',
      route: '/(main)/payment/scan-qr',
    },
    {
      id: 'bills',
      title: 'Pay Bills',
      icon: '🧾',
      color: 'bg-red-100',
      route: '/(main)/payment/bill-payment',
    },
    {
      id: 'airtime',
      title: 'Airtime',
      icon: '📞',
      color: 'bg-indigo-100',
      route: '/(main)/payment/airtime',
    },
  ];

  const handleQuickAction = (route: string) => {
    router.push(route as any);
  };

  const toggleBalanceVisibility = () => {
    setBalanceVisible(!balanceVisible);
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'payment': return '💳';
      case 'transfer': return '💸';
      case 'topup': return '⬆️';
      case 'bill_payment': return '🧾';
      case 'airtime': return '📞';
      default: return '💰';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'pending': return 'text-orange-600';
      case 'failed': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getAccountStatus = () => {
    if (!user) return null;
    
    if (!user.isVerified) {
      return {
        type: 'warning',
        message: 'Complete your verification to unlock all features',
        action: 'Verify Now',
      };
    }
    
    if (user.accountType === 'basic') {
      return {
        type: 'info',
        message: 'Upgrade to Premium for higher limits',
        action: 'Upgrade',
      };
    }
    
    return null;
  };

  const accountStatus = getAccountStatus();

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={handleRefresh} />
        }
      >
        <VStack className="flex-1" space="lg">
          {/* Header */}
          <Box className="bg-blue-600 px-6 pt-4 pb-8">
            <HStack className="justify-between items-center mb-6">
              <VStack>
                <Text className="text-white text-lg">{greeting},</Text>
                <Text className="text-white text-xl font-bold">
                  {user?.firstName} {user?.lastName}
                </Text>
              </VStack>
              <HStack space="sm">
                <Pressable className="w-10 h-10 bg-white/20 rounded-full items-center justify-center">
                  <Text className="text-white text-lg">🔔</Text>
                </Pressable>
                <Pressable className="w-10 h-10 bg-white/20 rounded-full items-center justify-center">
                  <Text className="text-white text-lg">⚙️</Text>
                </Pressable>
              </HStack>
            </HStack>

            {/* Balance Card */}
            <Card className="bg-white/10 border-white/20">
              <VStack space="sm" className="p-4">
                <HStack className="justify-between items-center">
                  <Text className="text-white/80 text-sm">Total Balance</Text>
                  <Pressable onPress={toggleBalanceVisibility}>
                    <Text className="text-white/80 text-lg">
                      {balanceVisible ? '👁️' : '🙈'}
                    </Text>
                  </Pressable>
                </HStack>
                
                <Text className="text-white text-3xl font-bold">
                  {balance && balanceVisible 
                    ? FormattingUtils.formatCurrency(balance.balance, balance.currType)
                    : '****'
                  }
                </Text>
                
                <HStack className="justify-between">
                  <VStack>
                    <Text className="text-white/80 text-xs">Basic Account</Text>
                    <Text className="text-white text-sm font-medium">
                      {balance && balanceVisible
                        ? FormattingUtils.formatCurrency(balance.basicAccountBalance, balance.currType)
                        : '****'
                      }
                    </Text>
                  </VStack>
                  <VStack>
                    <Text className="text-white/80 text-xs">Cashback</Text>
                    <Text className="text-white text-sm font-medium">
                      {balance && balanceVisible
                        ? FormattingUtils.formatCurrency(balance.cashbackAccountBalance || '0', balance.currType)
                        : '****'
                      }
                    </Text>
                  </VStack>
                </HStack>
              </VStack>
            </Card>
          </Box>

          {/* Account Status Alert */}
          {accountStatus && (
            <Box className="px-6">
              <Card className={`${
                accountStatus.type === 'warning' ? 'bg-orange-50 border-orange-200' : 'bg-blue-50 border-blue-200'
              }`}>
                <HStack className="p-4 items-center justify-between">
                  <VStack className="flex-1">
                    <Text className={`font-medium ${
                      accountStatus.type === 'warning' ? 'text-orange-800' : 'text-blue-800'
                    }`}>
                      {accountStatus.message}
                    </Text>
                  </VStack>
                  <Button size="sm" variant="outline">
                    <ButtonText className={
                      accountStatus.type === 'warning' ? 'text-orange-600' : 'text-blue-600'
                    }>
                      {accountStatus.action}
                    </ButtonText>
                  </Button>
                </HStack>
              </Card>
            </Box>
          )}

          {/* Quick Actions */}
          <VStack className="px-6" space="md">
            <Text className="text-lg font-semibold text-gray-800">Quick Actions</Text>
            <VStack space="md">
              {/* First Row */}
              <HStack className="justify-between">
                {quickActions.slice(0, 3).map((action) => (
                  <Pressable
                    key={action.id}
                    onPress={() => handleQuickAction(action.route)}
                    className="flex-1 items-center p-4 bg-white rounded-lg shadow-sm mx-1"
                  >
                    <Box className={`w-12 h-12 ${action.color} rounded-full items-center justify-center mb-2`}>
                      <Text className="text-xl">{action.icon}</Text>
                    </Box>
                    <Text className="text-gray-800 text-sm font-medium text-center">
                      {action.title}
                    </Text>
                  </Pressable>
                ))}
              </HStack>
              
              {/* Second Row */}
              <HStack className="justify-between">
                {quickActions.slice(3, 6).map((action) => (
                  <Pressable
                    key={action.id}
                    onPress={() => handleQuickAction(action.route)}
                    className="flex-1 items-center p-4 bg-white rounded-lg shadow-sm mx-1"
                  >
                    <Box className={`w-12 h-12 ${action.color} rounded-full items-center justify-center mb-2`}>
                      <Text className="text-xl">{action.icon}</Text>
                    </Box>
                    <Text className="text-gray-800 text-sm font-medium text-center">
                      {action.title}
                    </Text>
                  </Pressable>
                ))}
              </HStack>
            </VStack>
          </VStack>

          {/* Recent Transactions */}
          <VStack className="px-6" space="md">
            <HStack className="justify-between items-center">
              <Text className="text-lg font-semibold text-gray-800">Recent Transactions</Text>
              <Pressable onPress={() => router.push('/(main)/(tabs)/history')}>
                <Text className="text-blue-600 text-sm">View All</Text>
              </Pressable>
            </HStack>

            <Card className="bg-white">
              <VStack className="p-4" space="sm">
                {transactions.length > 0 ? (
                  transactions.slice(0, 5).map((transaction, index) => (
                    <VStack key={transaction.id}>
                      <HStack className="justify-between items-center py-3">
                        <HStack className="items-center" space="md">
                          <Box className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center">
                            <Text className="text-gray-600">
                              {getTransactionIcon(transaction.type)}
                            </Text>
                          </Box>
                          <VStack>
                            <Text className="font-medium text-gray-800">
                              {transaction.description}
                            </Text>
                            <Text className="text-xs text-gray-500">
                              {FormattingUtils.formatRelativeTime(transaction.createdAt)}
                            </Text>
                          </VStack>
                        </HStack>
                        <VStack className="items-end">
                          <Text className={`font-semibold ${
                            transaction.type === 'topup' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {FormattingUtils.formatTransactionAmount(
                              transaction.amount, 
                              transaction.type, 
                              transaction.currency
                            )}
                          </Text>
                          <Badge
                            size="sm"
                            variant="outline"
                            className={getStatusColor(transaction.status)}
                          >
                            <BadgeText className={getStatusColor(transaction.status)}>
                              {transaction.status}
                            </BadgeText>
                          </Badge>
                        </VStack>
                      </HStack>
                      {index < transactions.slice(0, 5).length - 1 && <Divider />}
                    </VStack>
                  ))
                ) : (
                  <EmptyState
                    icon="📊"
                    title="No transactions yet"
                    description="Start using OneMoney to see your transaction history"
                    actionText="Make a Payment"
                    onAction={() => router.push('/(main)/(tabs)/payments')}
                  />
                )}
              </VStack>
            </Card>
          </VStack>
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
}
