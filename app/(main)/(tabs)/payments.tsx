import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  SafeAreaView,
  ScrollView,
  Pressable,
  Card,
} from '@/components/ui';
import { router } from 'expo-router';

export default function PaymentsScreen() {
  const paymentOptions = [
    {
      id: 'scan-qr',
      title: 'Scan QR Code',
      description: 'Scan to pay merchants',
      icon: '📱',
      color: 'bg-blue-100',
      textColor: 'text-blue-600',
      route: '/(main)/payment/scan-qr',
    },
    {
      id: 'make-payment',
      title: 'Make Payment',
      description: 'Send money to anyone',
      icon: '💳',
      color: 'bg-green-100',
      textColor: 'text-green-600',
      route: '/(main)/payment/make-payment',
    },
    {
      id: 'bill-payment',
      title: 'Pay Bills',
      description: 'Utilities, school fees, etc.',
      icon: '🧾',
      color: 'bg-purple-100',
      textColor: 'text-purple-600',
      route: '/(main)/payment/bill-payment',
    },
    {
      id: 'airtime',
      title: 'Buy Airtime',
      description: 'Mobile credit & data bundles',
      icon: '📞',
      color: 'bg-orange-100',
      textColor: 'text-orange-600',
      route: '/(main)/payment/airtime',
    },
  ];

  const handlePaymentOption = (route: string) => {
    router.push(route as any);
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <VStack className="flex-1" space="lg">
        {/* Header */}
        <Box className="bg-white px-6 py-4 border-b border-gray-200">
          <Text className="text-2xl font-bold text-gray-800">Payments</Text>
          <Text className="text-gray-600">Choose a payment option</Text>
        </Box>

        <ScrollView className="flex-1">
          <VStack className="px-6" space="md">
            {/* Payment Options Grid */}
            <VStack space="md">
              {paymentOptions.map((option) => (
                <Pressable
                  key={option.id}
                  onPress={() => handlePaymentOption(option.route)}
                >
                  <Card className="bg-white">
                    <HStack className="p-4 items-center" space="md">
                      <Box className={`w-12 h-12 ${option.color} rounded-full items-center justify-center`}>
                        <Text className="text-xl">{option.icon}</Text>
                      </Box>
                      <VStack className="flex-1">
                        <Text className="font-semibold text-gray-800 text-lg">
                          {option.title}
                        </Text>
                        <Text className="text-gray-600 text-sm">
                          {option.description}
                        </Text>
                      </VStack>
                      <Box className="w-6 h-6 items-center justify-center">
                        <Text className="text-gray-400">›</Text>
                      </Box>
                    </HStack>
                  </Card>
                </Pressable>
              ))}
            </VStack>

            {/* Recent Payments */}
            <VStack space="md" className="mt-8">
              <Text className="text-lg font-semibold text-gray-800">Recent Payments</Text>
              <Card className="bg-white">
                <VStack className="p-4 items-center" space="sm">
                  <Text className="text-gray-500">No recent payments</Text>
                  <Text className="text-gray-400 text-sm text-center">
                    Your recent payment activities will appear here
                  </Text>
                </VStack>
              </Card>
            </VStack>
          </VStack>
        </ScrollView>
      </VStack>
    </SafeAreaView>
  );
}
