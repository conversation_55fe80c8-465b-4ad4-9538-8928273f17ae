import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  SafeAreaView,
  ScrollView,
  Pressable,
  Card,
} from '@/components/ui';
import { router } from 'expo-router';

export default function TransferScreen() {
  const transferOptions = [
    {
      id: 'send-money',
      title: 'Send Money',
      description: 'Transfer to contacts',
      icon: '💸',
      color: 'bg-blue-100',
      textColor: 'text-blue-600',
      route: '/(main)/transfer/send-money',
    },
    {
      id: 'bank-transfer',
      title: 'Bank Transfer',
      description: 'Transfer to bank accounts',
      icon: '🏦',
      color: 'bg-green-100',
      textColor: 'text-green-600',
      route: '/(main)/transfer/bank-transfer',
    },
    {
      id: 'contacts',
      title: 'My Contacts',
      description: 'Manage transfer contacts',
      icon: '👥',
      color: 'bg-purple-100',
      textColor: 'text-purple-600',
      route: '/(main)/transfer/contacts',
    },
  ];

  const handleTransferOption = (route: string) => {
    router.push(route as any);
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <VStack className="flex-1" space="lg">
        {/* Header */}
        <Box className="bg-white px-6 py-4 border-b border-gray-200">
          <Text className="text-2xl font-bold text-gray-800">Transfer</Text>
          <Text className="text-gray-600">Send money to anyone</Text>
        </Box>

        <ScrollView className="flex-1">
          <VStack className="px-6" space="md">
            {/* Transfer Options */}
            <VStack space="md">
              {transferOptions.map((option) => (
                <Pressable
                  key={option.id}
                  onPress={() => handleTransferOption(option.route)}
                >
                  <Card className="bg-white">
                    <HStack className="p-4 items-center" space="md">
                      <Box className={`w-12 h-12 ${option.color} rounded-full items-center justify-center`}>
                        <Text className="text-xl">{option.icon}</Text>
                      </Box>
                      <VStack className="flex-1">
                        <Text className="font-semibold text-gray-800 text-lg">
                          {option.title}
                        </Text>
                        <Text className="text-gray-600 text-sm">
                          {option.description}
                        </Text>
                      </VStack>
                      <Box className="w-6 h-6 items-center justify-center">
                        <Text className="text-gray-400">›</Text>
                      </Box>
                    </HStack>
                  </Card>
                </Pressable>
              ))}
            </VStack>

            {/* Recent Transfers */}
            <VStack space="md" className="mt-8">
              <Text className="text-lg font-semibold text-gray-800">Recent Transfers</Text>
              <Card className="bg-white">
                <VStack className="p-4 items-center" space="sm">
                  <Text className="text-gray-500">No recent transfers</Text>
                  <Text className="text-gray-400 text-sm text-center">
                    Your recent transfer activities will appear here
                  </Text>
                </VStack>
              </Card>
            </VStack>
          </VStack>
        </ScrollView>
      </VStack>
    </SafeAreaView>
  );
}
