import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  SafeAreaView,
  ScrollView,
  Card,
  Divider,
} from '@/components/ui';
import { useWallet } from '@/src/stores/walletStore';

export default function HistoryScreen() {
  const { transactions } = useWallet();
  const [selectedFilter, setSelectedFilter] = useState('all');

  const filters = [
    { id: 'all', label: 'All' },
    { id: 'payment', label: 'Payments' },
    { id: 'transfer', label: 'Transfers' },
    { id: 'topup', label: 'Top-ups' },
  ];

  const filteredTransactions = transactions.filter(transaction => 
    selectedFilter === 'all' || transaction.type === selectedFilter
  );

  const formatCurrency = (amount: string, currency: string) => {
    return `${currency} ${parseFloat(amount).toFixed(2)}`;
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'payment': return '💳';
      case 'transfer': return '💸';
      case 'topup': return '⬆️';
      default: return '💰';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'pending': return 'text-orange-600';
      case 'failed': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <VStack className="flex-1" space="lg">
        {/* Header */}
        <Box className="bg-white px-6 py-4 border-b border-gray-200">
          <Text className="text-2xl font-bold text-gray-800">Transaction History</Text>
          <Text className="text-gray-600">View all your transactions</Text>
        </Box>

        {/* Filter Tabs */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} className="px-6">
          <HStack space="sm">
            {filters.map((filter) => (
              <Box
                key={filter.id}
                className={`px-4 py-2 rounded-full ${
                  selectedFilter === filter.id
                    ? 'bg-blue-600'
                    : 'bg-white border border-gray-300'
                }`}
              >
                <Text
                  className={`text-sm font-medium ${
                    selectedFilter === filter.id ? 'text-white' : 'text-gray-600'
                  }`}
                  onPress={() => setSelectedFilter(filter.id)}
                >
                  {filter.label}
                </Text>
              </Box>
            ))}
          </HStack>
        </ScrollView>

        {/* Transactions List */}
        <ScrollView className="flex-1 px-6">
          <Card className="bg-white">
            <VStack className="p-4" space="sm">
              {filteredTransactions.length > 0 ? (
                filteredTransactions.map((transaction, index) => (
                  <VStack key={transaction.id}>
                    <HStack className="justify-between items-center py-3">
                      <HStack className="items-center" space="md">
                        <Box className="w-12 h-12 bg-gray-100 rounded-full items-center justify-center">
                          <Text className="text-lg">
                            {getTransactionIcon(transaction.type)}
                          </Text>
                        </Box>
                        <VStack>
                          <Text className="font-medium text-gray-800">
                            {transaction.description}
                          </Text>
                          <Text className="text-xs text-gray-500">
                            {new Date(transaction.createdAt).toLocaleString()}
                          </Text>
                          <Text className={`text-xs font-medium ${getStatusColor(transaction.status)}`}>
                            {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                          </Text>
                        </VStack>
                      </HStack>
                      <VStack className="items-end">
                        <Text className={`font-semibold ${
                          transaction.type === 'topup' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {transaction.type === 'topup' ? '+' : '-'}
                          {formatCurrency(transaction.amount, transaction.currency)}
                        </Text>
                        {transaction.fee && (
                          <Text className="text-xs text-gray-500">
                            Fee: {formatCurrency(transaction.fee, transaction.currency)}
                          </Text>
                        )}
                      </VStack>
                    </HStack>
                    {index < filteredTransactions.length - 1 && <Divider />}
                  </VStack>
                ))
              ) : (
                <VStack className="items-center py-8" space="sm">
                  <Text className="text-gray-500">No transactions found</Text>
                  <Text className="text-gray-400 text-sm text-center">
                    {selectedFilter === 'all' 
                      ? 'Start using OneMoney to see your transaction history'
                      : `No ${selectedFilter} transactions found`
                    }
                  </Text>
                </VStack>
              )}
            </VStack>
          </Card>
        </ScrollView>
      </VStack>
    </SafeAreaView>
  );
}
