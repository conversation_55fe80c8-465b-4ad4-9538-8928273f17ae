import React from 'react';
import { Alert } from 'react-native';
import {
  Box,
  VStack,
  HStack,
  Text,
  SafeAreaView,
  ScrollView,
  Pressable,
  Card,
  Avatar,
  AvatarFallbackText,
  Divider,
} from '@/components/ui';
import { useAuth } from '@/src/stores/authStore';
import { router } from 'expo-router';

export default function ProfileScreen() {
  const { user, logout } = useAuth();

  const profileOptions = [
    {
      id: 'personal-info',
      title: 'Personal Information',
      icon: '👤',
      route: '/profile/personal-info',
    },
    {
      id: 'security',
      title: 'Security Settings',
      icon: '🔒',
      route: '/profile/security',
    },
    {
      id: 'payment-methods',
      title: 'Payment Methods',
      icon: '💳',
      route: '/profile/payment-methods',
    },
    {
      id: 'notifications',
      title: 'Notifications',
      icon: '🔔',
      route: '/profile/notifications',
    },
    {
      id: 'help',
      title: 'Help & Support',
      icon: '❓',
      route: '/profile/help',
    },
    {
      id: 'about',
      title: 'About',
      icon: 'ℹ️',
      route: '/profile/about',
    },
  ];

  const handleOptionPress = (route: string) => {
    // For now, just show an alert since these screens aren't implemented yet
    Alert.alert('Coming Soon', 'This feature will be available soon!');
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive',
          onPress: async () => {
            await logout();
            router.replace('/(auth)/login');
          }
        },
      ]
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <VStack className="flex-1" space="lg">
        {/* Header */}
        <Box className="bg-white px-6 py-4 border-b border-gray-200">
          <Text className="text-2xl font-bold text-gray-800">Profile</Text>
        </Box>

        <ScrollView className="flex-1">
          <VStack className="px-6" space="lg">
            {/* User Info Card */}
            <Card className="bg-white">
              <VStack className="p-6 items-center" space="md">
                <Avatar size="xl">
                  <AvatarFallbackText>
                    {user?.firstName?.[0]}{user?.lastName?.[0]}
                  </AvatarFallbackText>
                </Avatar>
                <VStack className="items-center" space="xs">
                  <Text className="text-xl font-bold text-gray-800">
                    {user?.firstName} {user?.lastName}
                  </Text>
                  <Text className="text-gray-600">{user?.phoneNumber}</Text>
                  <Text className="text-gray-600">{user?.email}</Text>
                  <Box className={`px-3 py-1 rounded-full ${
                    user?.isVerified ? 'bg-green-100' : 'bg-orange-100'
                  }`}>
                    <Text className={`text-xs font-medium ${
                      user?.isVerified ? 'text-green-600' : 'text-orange-600'
                    }`}>
                      {user?.isVerified ? 'Verified' : 'Unverified'}
                    </Text>
                  </Box>
                </VStack>
              </VStack>
            </Card>

            {/* Profile Options */}
            <Card className="bg-white">
              <VStack className="p-4" space="sm">
                {profileOptions.map((option, index) => (
                  <VStack key={option.id}>
                    <Pressable onPress={() => handleOptionPress(option.route)}>
                      <HStack className="items-center py-3" space="md">
                        <Box className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center">
                          <Text className="text-lg">{option.icon}</Text>
                        </Box>
                        <Text className="flex-1 font-medium text-gray-800">
                          {option.title}
                        </Text>
                        <Box className="w-6 h-6 items-center justify-center">
                          <Text className="text-gray-400">›</Text>
                        </Box>
                      </HStack>
                    </Pressable>
                    {index < profileOptions.length - 1 && <Divider />}
                  </VStack>
                ))}
              </VStack>
            </Card>

            {/* Logout Button */}
            <Pressable onPress={handleLogout}>
              <Card className="bg-white">
                <HStack className="p-4 items-center justify-center" space="md">
                  <Text className="text-lg">🚪</Text>
                  <Text className="font-medium text-red-600">Logout</Text>
                </HStack>
              </Card>
            </Pressable>

            {/* App Version */}
            <VStack className="items-center py-4" space="xs">
              <Text className="text-gray-500 text-sm">OneMoney</Text>
              <Text className="text-gray-400 text-xs">Version 1.0.0</Text>
            </VStack>
          </VStack>
        </ScrollView>
      </VStack>
    </SafeAreaView>
  );
}
