import React, { useState } from 'react';
import { Alert, Platform } from 'react-native';
import { router } from 'expo-router';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonText,
  Input,
  InputField,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  SafeAreaView,
  KeyboardAvoidingView,
  ScrollView,
  Pressable,
  Card,
  Checkbox,
  CheckboxIndicator,
  CheckboxIcon,
  CheckboxLabel,
  CheckIcon,
} from '@/components/ui';
import { useForm } from '@/src/hooks/useForm';
import { ValidationUtils } from '@/src/utils/validation';
import { FormattingUtils } from '@/src/utils/formatting';
import { LoadingSpinner } from '@/src/components/LoadingSpinner';
import { topupApi } from '@/src/services/api';

interface BankCardFormData {
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardHolderName: string;
  saveCard: boolean;
}

export default function BankCardScreen() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    values,
    errors,
    isValid,
    setValue,
    validate,
    handleSubmit,
  } = useForm<BankCardFormData>({
    initialValues: {
      cardNumber: '',
      expiryDate: '',
      cvv: '',
      cardHolderName: '',
      saveCard: false,
    },
    validationRules: {
      cardNumber: ValidationUtils.validateCardNumber,
      expiryDate: ValidationUtils.validateExpiryDate,
      cvv: ValidationUtils.validateCVV,
      cardHolderName: ValidationUtils.validateName,
      saveCard: () => ({ isValid: true }), // Optional field
    },
    onSubmit: handleAddCard,
  });

  async function handleAddCard(formData: BankCardFormData) {
    setIsProcessing(true);
    setError(null);

    try {
      const response = await topupApi.addBankCard({
        cardNumber: FormattingUtils.parseCardNumber(formData.cardNumber),
        expiryDate: formData.expiryDate,
        cvv: formData.cvv,
        cardHolderName: formData.cardHolderName,
        saveCard: formData.saveCard,
      });

      if (response.success) {
        Alert.alert(
          'Card Added Successfully',
          'Your bank card has been added and is ready for use.',
          [
            {
              text: 'OK',
              onPress: () => router.back(),
            },
          ]
        );
      } else {
        setError(response.error || 'Failed to add card. Please try again.');
        Alert.alert('Failed to Add Card', response.error || 'Please try again.');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to add card. Please try again.';
      setError(errorMessage);
      Alert.alert('Failed to Add Card', errorMessage);
    } finally {
      setIsProcessing(false);
    }
  }

  const handleGoBack = () => {
    router.back();
  };

  const handleCardNumberChange = (text: string) => {
    const formatted = FormattingUtils.formatCardNumber(text);
    setValue('cardNumber', formatted);
  };

  const handleExpiryDateChange = (text: string) => {
    const formatted = FormattingUtils.formatExpiryDate(text);
    setValue('expiryDate', formatted);
  };

  const getCardType = (cardNumber: string) => {
    const cleaned = cardNumber.replace(/\D/g, '');
    if (cleaned.startsWith('4')) return 'Visa';
    if (cleaned.startsWith('5') || cleaned.startsWith('2')) return 'Mastercard';
    return 'Card';
  };

  const cardType = getCardType(values.cardNumber);

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1">
        {/* Header */}
        <HStack className="justify-between items-center p-4 bg-white border-b border-gray-200">
          <Pressable onPress={handleGoBack}>
            <Box className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center">
              <Text className="text-gray-600 text-lg">←</Text>
            </Box>
          </Pressable>
          
          <Text className="text-lg font-semibold text-gray-800">Add Bank Card</Text>
          
          <Box className="w-10 h-10" />
        </HStack>

        <ScrollView className="flex-1">
          <VStack className="p-6" space="lg">
            {/* Card Preview */}
            <Card className="bg-gradient-to-r from-gray-800 to-gray-900 min-h-48">
              <VStack className="p-6 justify-between h-48">
                <HStack className="justify-between items-start">
                  <Text className="text-white text-lg font-bold">{cardType}</Text>
                  <Box className="w-12 h-8 bg-yellow-400 rounded opacity-80" />
                </HStack>
                
                <VStack space="md">
                  <Text className="text-white text-xl font-mono tracking-wider">
                    {values.cardNumber || '•••• •••• •••• ••••'}
                  </Text>
                  
                  <HStack className="justify-between">
                    <VStack>
                      <Text className="text-gray-300 text-xs">CARD HOLDER</Text>
                      <Text className="text-white text-sm font-medium">
                        {values.cardHolderName || 'YOUR NAME'}
                      </Text>
                    </VStack>
                    <VStack>
                      <Text className="text-gray-300 text-xs">EXPIRES</Text>
                      <Text className="text-white text-sm font-medium">
                        {values.expiryDate || 'MM/YY'}
                      </Text>
                    </VStack>
                  </HStack>
                </VStack>
              </VStack>
            </Card>

            {/* Card Form */}
            <VStack space="md">
              <FormControl isInvalid={!!errors.cardNumber}>
                <FormControlLabel>
                  <FormControlLabelText>Card Number</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="1234 5678 9012 3456"
                    value={values.cardNumber}
                    onChangeText={handleCardNumberChange}
                    keyboardType="numeric"
                    maxLength={19}
                    onBlur={() => validate('cardNumber')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.cardNumber}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <HStack space="md">
                <FormControl isInvalid={!!errors.expiryDate} className="flex-1">
                  <FormControlLabel>
                    <FormControlLabelText>Expiry Date</FormControlLabelText>
                  </FormControlLabel>
                  <Input>
                    <InputField
                      placeholder="MM/YY"
                      value={values.expiryDate}
                      onChangeText={handleExpiryDateChange}
                      keyboardType="numeric"
                      maxLength={5}
                      onBlur={() => validate('expiryDate')}
                    />
                  </Input>
                  <FormControlError>
                    <FormControlErrorText>{errors.expiryDate}</FormControlErrorText>
                  </FormControlError>
                </FormControl>

                <FormControl isInvalid={!!errors.cvv} className="flex-1">
                  <FormControlLabel>
                    <FormControlLabelText>CVV</FormControlLabelText>
                  </FormControlLabel>
                  <Input>
                    <InputField
                      placeholder="123"
                      value={values.cvv}
                      onChangeText={(text) => setValue('cvv', text)}
                      keyboardType="numeric"
                      maxLength={4}
                      secureTextEntry
                      onBlur={() => validate('cvv')}
                    />
                  </Input>
                  <FormControlError>
                    <FormControlErrorText>{errors.cvv}</FormControlErrorText>
                  </FormControlError>
                </FormControl>
              </HStack>

              <FormControl isInvalid={!!errors.cardHolderName}>
                <FormControlLabel>
                  <FormControlLabelText>Card Holder Name</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="John Doe"
                    value={values.cardHolderName}
                    onChangeText={(text) => setValue('cardHolderName', text.toUpperCase())}
                    autoCapitalize="characters"
                    onBlur={() => validate('cardHolderName')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.cardHolderName}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl>
                <Checkbox
                  value={values.saveCard}
                  onChange={(checked) => setValue('saveCard', checked)}
                  className="mt-4"
                >
                  <CheckboxIndicator>
                    <CheckboxIcon as={CheckIcon} />
                  </CheckboxIndicator>
                  <CheckboxLabel className="ml-2">
                    <Text className="text-sm text-gray-700">
                      Save this card for future transactions
                    </Text>
                  </CheckboxLabel>
                </Checkbox>
              </FormControl>
            </VStack>

            {/* Security Information */}
            <Card className="bg-blue-50 border-blue-200">
              <VStack className="p-4" space="sm">
                <HStack className="items-center" space="sm">
                  <Text className="text-blue-600 text-lg">🔒</Text>
                  <Text className="text-blue-800 font-semibold">Your card is secure</Text>
                </HStack>
                <Text className="text-blue-700 text-sm">
                  We use bank-level encryption to protect your card information. 
                  Your CVV is never stored and card details are encrypted.
                </Text>
              </VStack>
            </Card>

            {/* Supported Cards */}
            <VStack space="sm">
              <Text className="text-gray-600 text-sm">Supported Cards</Text>
              <HStack className="justify-start" space="md">
                <Box className="w-12 h-8 bg-blue-600 rounded items-center justify-center">
                  <Text className="text-white text-xs font-bold">VISA</Text>
                </Box>
                <Box className="w-12 h-8 bg-red-600 rounded items-center justify-center">
                  <Text className="text-white text-xs font-bold">MC</Text>
                </Box>
                <Box className="w-12 h-8 bg-gray-600 rounded items-center justify-center">
                  <Text className="text-white text-xs font-bold">LOCAL</Text>
                </Box>
              </HStack>
            </VStack>

            {/* Submit Button */}
            <Button
              onPress={handleSubmit}
              isDisabled={isProcessing || !isValid}
              className="mt-4"
            >
              <ButtonText>
                {isProcessing ? 'Adding Card...' : 'Add Card'}
              </ButtonText>
            </Button>

            {/* Terms */}
            <Text className="text-gray-500 text-xs text-center">
              By adding this card, you agree to our{' '}
              <Text className="text-blue-600 underline">Terms of Service</Text>
              {' '}and{' '}
              <Text className="text-blue-600 underline">Privacy Policy</Text>
            </Text>
          </VStack>
        </ScrollView>
      </KeyboardAvoidingView>

      {isProcessing && (
        <LoadingSpinner message="Adding your card..." overlay />
      )}
    </SafeAreaView>
  );
}
