import React, { useState } from 'react';
import { Alert, Platform } from 'react-native';
import { router } from 'expo-router';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonText,
  Input,
  InputField,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  SafeAreaView,
  KeyboardAvoidingView,
  ScrollView,
  Pressable,
  Card,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  ChevronDownIcon,
} from '@/components/ui';
import { useWallet } from '@/src/stores/walletStore';
import { useForm } from '@/src/hooks/useForm';
import { ValidationUtils } from '@/src/utils/validation';
import { FormattingUtils } from '@/src/utils/formatting';
import { LoadingSpinner } from '@/src/components/LoadingSpinner';
import { topupApi } from '@/src/services/api';

interface TopUpFormData {
  amount: string;
  paymentMethod: string;
}

export default function TopUpScreen() {
  const { balance, paymentMethods, refreshWallet } = useWallet();
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const quickAmounts = ['10', '20', '50', '100', '200', '500'];

  const {
    values,
    errors,
    isValid,
    setValue,
    validate,
    handleSubmit,
  } = useForm<TopUpFormData>({
    initialValues: {
      amount: '',
      paymentMethod: '',
    },
    validationRules: {
      amount: ValidationUtils.validateAmount,
      paymentMethod: (value) => ValidationUtils.validateRequired(value, 'Payment method'),
    },
    onSubmit: handleTopUp,
  });

  async function handleTopUp(formData: TopUpFormData) {
    setIsProcessing(true);
    setError(null);

    try {
      const response = await topupApi.topUp({
        amount: formData.amount,
        paymentMethodId: formData.paymentMethod,
        currency: balance?.currType || 'USD',
      });

      if (response.success) {
        await refreshWallet(); // Refresh balance after successful top-up
        
        Alert.alert(
          'Top-Up Successful',
          `Your wallet has been topped up with ${FormattingUtils.formatCurrency(formData.amount, balance?.currType || 'USD')}!`,
          [
            {
              text: 'OK',
              onPress: () => router.push('/(main)/(tabs)/home'),
            },
          ]
        );
      } else {
        setError(response.error || 'Top-up failed. Please try again.');
        Alert.alert('Top-Up Failed', response.error || 'Please try again.');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Top-up failed. Please try again.';
      setError(errorMessage);
      Alert.alert('Top-Up Failed', errorMessage);
    } finally {
      setIsProcessing(false);
    }
  }

  const handleQuickAmount = (amount: string) => {
    setValue('amount', amount);
  };

  const handleAddCard = () => {
    router.push('/(main)/topup/bank-card');
  };

  const handleGoBack = () => {
    router.back();
  };

  const topUpAmount = values.amount ? parseFloat(values.amount) : 0;
  const fee = topUpAmount * 0.02; // 2% fee
  const totalAmount = topUpAmount + fee;

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1">
        {/* Header */}
        <HStack className="justify-between items-center p-4 bg-white border-b border-gray-200">
          <Pressable onPress={handleGoBack}>
            <Box className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center">
              <Text className="text-gray-600 text-lg">←</Text>
            </Box>
          </Pressable>
          
          <Text className="text-lg font-semibold text-gray-800">Top Up Wallet</Text>
          
          <Box className="w-10 h-10" />
        </HStack>

        <ScrollView className="flex-1">
          <VStack className="p-6" space="lg">
            {/* Current Balance */}
            <Card className="bg-gradient-to-r from-blue-500 to-blue-600">
              <VStack className="p-6" space="sm">
                <Text className="text-white/80 text-sm">Current Balance</Text>
                <Text className="text-white text-3xl font-bold">
                  {balance ? FormattingUtils.formatCurrency(balance.balance, balance.currType) : '---'}
                </Text>
                <Text className="text-white/80 text-sm">
                  Add money to your wallet for seamless transactions
                </Text>
              </VStack>
            </Card>

            {/* Quick Amount Selection */}
            <VStack space="md">
              <Text className="text-lg font-semibold text-gray-800">Quick Amounts</Text>
              <VStack space="md">
                {/* First Row */}
                <HStack className="justify-between">
                  {quickAmounts.slice(0, 3).map((amount) => (
                    <Pressable
                      key={amount}
                      onPress={() => handleQuickAmount(amount)}
                      className={`flex-1 p-4 rounded-lg border-2 mx-1 ${
                        values.amount === amount 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 bg-white'
                      }`}
                    >
                      <Text className={`text-center font-medium ${
                        values.amount === amount ? 'text-blue-600' : 'text-gray-800'
                      }`}>
                        {FormattingUtils.formatCurrency(amount, balance?.currType || 'USD')}
                      </Text>
                    </Pressable>
                  ))}
                </HStack>
                
                {/* Second Row */}
                <HStack className="justify-between">
                  {quickAmounts.slice(3, 6).map((amount) => (
                    <Pressable
                      key={amount}
                      onPress={() => handleQuickAmount(amount)}
                      className={`flex-1 p-4 rounded-lg border-2 mx-1 ${
                        values.amount === amount 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 bg-white'
                      }`}
                    >
                      <Text className={`text-center font-medium ${
                        values.amount === amount ? 'text-blue-600' : 'text-gray-800'
                      }`}>
                        {FormattingUtils.formatCurrency(amount, balance?.currType || 'USD')}
                      </Text>
                    </Pressable>
                  ))}
                </HStack>
              </VStack>
            </VStack>

            {/* Custom Amount */}
            <VStack space="md">
              <FormControl isInvalid={!!errors.amount}>
                <FormControlLabel>
                  <FormControlLabelText>Custom Amount</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="Enter amount"
                    value={values.amount}
                    onChangeText={(text) => setValue('amount', text)}
                    keyboardType="decimal-pad"
                    onBlur={() => validate('amount')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.amount}</FormControlErrorText>
                </FormControlError>
              </FormControl>
            </VStack>

            {/* Payment Method Selection */}
            <VStack space="md">
              <HStack className="justify-between items-center">
                <Text className="text-lg font-semibold text-gray-800">Payment Method</Text>
                <Pressable onPress={handleAddCard}>
                  <Text className="text-blue-600 text-sm">+ Add Card</Text>
                </Pressable>
              </HStack>

              <FormControl isInvalid={!!errors.paymentMethod}>
                {paymentMethods.length > 0 ? (
                  <VStack space="sm">
                    {paymentMethods.map((method) => (
                      <Pressable
                        key={method.id}
                        onPress={() => setValue('paymentMethod', method.id)}
                      >
                        <Card className={`${
                          values.paymentMethod === method.id 
                            ? 'border-blue-500 bg-blue-50' 
                            : 'bg-white'
                        }`}>
                          <HStack className="p-4 items-center justify-between">
                            <HStack className="items-center" space="md">
                              <Box className="w-12 h-12 bg-gray-100 rounded-lg items-center justify-center">
                                <Text className="text-xl">
                                  {method.type === 'bank_card' ? '💳' : '🏦'}
                                </Text>
                              </Box>
                              <VStack>
                                <Text className="font-medium text-gray-800">{method.name}</Text>
                                <Text className="text-sm text-gray-600">{method.identifier}</Text>
                              </VStack>
                            </HStack>
                            {values.paymentMethod === method.id && (
                              <Text className="text-blue-600 text-xl">✓</Text>
                            )}
                          </HStack>
                        </Card>
                      </Pressable>
                    ))}
                  </VStack>
                ) : (
                  <Card className="bg-gray-50">
                    <VStack className="p-6 items-center" space="md">
                      <Text className="text-2xl">💳</Text>
                      <VStack className="items-center" space="sm">
                        <Text className="font-medium text-gray-800">No Payment Methods</Text>
                        <Text className="text-gray-600 text-sm text-center">
                          Add a bank card to top up your wallet
                        </Text>
                      </VStack>
                      <Button onPress={handleAddCard} size="sm">
                        <ButtonText>Add Bank Card</ButtonText>
                      </Button>
                    </VStack>
                  </Card>
                )}
                <FormControlError>
                  <FormControlErrorText>{errors.paymentMethod}</FormControlErrorText>
                </FormControlError>
              </FormControl>
            </VStack>

            {/* Top-Up Summary */}
            {values.amount && values.paymentMethod && (
              <Card className="bg-gray-50">
                <VStack className="p-4" space="sm">
                  <Text className="font-semibold text-gray-800">Top-Up Summary</Text>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Amount</Text>
                    <Text className="font-medium">
                      {FormattingUtils.formatCurrency(values.amount, balance?.currType || 'USD')}
                    </Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Processing Fee (2%)</Text>
                    <Text className="font-medium">
                      {FormattingUtils.formatCurrency(fee.toString(), balance?.currType || 'USD')}
                    </Text>
                  </HStack>
                  <Divider />
                  <HStack className="justify-between">
                    <Text className="font-semibold text-gray-800">Total Charge</Text>
                    <Text className="font-bold text-lg">
                      {FormattingUtils.formatCurrency(totalAmount.toString(), balance?.currType || 'USD')}
                    </Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="font-semibold text-gray-800">Wallet Credit</Text>
                    <Text className="font-bold text-lg text-green-600">
                      +{FormattingUtils.formatCurrency(values.amount, balance?.currType || 'USD')}
                    </Text>
                  </HStack>
                </VStack>
              </Card>
            )}

            {/* Submit Button */}
            <Button
              onPress={handleSubmit}
              isDisabled={isProcessing || !isValid || paymentMethods.length === 0}
              className="mt-4"
            >
              <ButtonText>
                {isProcessing ? 'Processing...' : 'Top Up Wallet'}
              </ButtonText>
            </Button>

            {/* Security Notice */}
            <Card className="bg-blue-50 border-blue-200">
              <HStack className="p-4 items-center" space="md">
                <Text className="text-blue-600 text-xl">🔒</Text>
                <VStack className="flex-1">
                  <Text className="text-blue-800 font-medium text-sm">Secure Transaction</Text>
                  <Text className="text-blue-700 text-xs">
                    Your payment information is encrypted and secure
                  </Text>
                </VStack>
              </HStack>
            </Card>
          </VStack>
        </ScrollView>
      </KeyboardAvoidingView>

      {isProcessing && (
        <LoadingSpinner message="Processing top-up..." overlay />
      )}
    </SafeAreaView>
  );
}
