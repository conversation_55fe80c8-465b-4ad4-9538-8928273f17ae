import React, { useState } from 'react';
import { Alert, Platform } from 'react-native';
import { router } from 'expo-router';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonText,
  Input,
  InputField,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  SafeAreaView,
  KeyboardAvoidingView,
  ScrollView,
  Pressable,
  Card,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  ChevronDownIcon,
  Divider,
} from '@/components/ui';
import { useWallet } from '@/src/stores/walletStore';
import { useForm } from '@/src/hooks/useForm';
import { ValidationUtils } from '@/src/utils/validation';
import { FormattingUtils } from '@/src/utils/formatting';
import { LoadingSpinner } from '@/src/components/LoadingSpinner';
import { transferApi } from '@/src/services/api';

interface BankTransferFormData {
  bank: string;
  accountNumber: string;
  accountHolderName: string;
  amount: string;
  description: string;
}

export default function BankTransferScreen() {
  const { balance, refreshWallet } = useWallet();
  const [isProcessing, setIsProcessing] = useState(false);

  const banks = [
    { id: 'cabs', name: 'CABS', code: 'CABS' },
    { id: 'cbz', name: 'CBZ Bank', code: 'CBZ' },
    { id: 'fbc', name: 'FBC Bank', code: 'FBC' },
    { id: 'nedbank', name: 'Nedbank', code: 'NEDBANK' },
    { id: 'stanbic', name: 'Stanbic Bank', code: 'STANBIC' },
    { id: 'steward', name: 'Steward Bank', code: 'STEWARD' },
    { id: 'zvb', name: 'ZVB Bank', code: 'ZVB' },
  ];

  const {
    values,
    errors,
    isValid,
    setValue,
    validate,
    handleSubmit,
  } = useForm<BankTransferFormData>({
    initialValues: {
      bank: '',
      accountNumber: '',
      accountHolderName: '',
      amount: '',
      description: '',
    },
    validationRules: {
      bank: (value) => ValidationUtils.validateRequired(value, 'Bank'),
      accountNumber: (value) => ValidationUtils.validateRequired(value, 'Account number'),
      accountHolderName: ValidationUtils.validateName,
      amount: ValidationUtils.validateAmount,
      description: (value) => ValidationUtils.validateRequired(value, 'Description'),
    },
    onSubmit: handleBankTransfer,
  });

  async function handleBankTransfer(formData: BankTransferFormData) {
    // Check if user has sufficient balance
    const transferAmount = parseFloat(formData.amount);
    const fee = transferAmount * 0.01; // 1% fee for bank transfers
    const totalAmount = transferAmount + fee;

    if (balance && totalAmount > parseFloat(balance.balance)) {
      Alert.alert('Insufficient Funds', 'You do not have enough balance for this transfer including fees.');
      return;
    }

    setIsProcessing(true);

    try {
      const response = await transferApi.sendMoney({
        type: 'bank_transfer',
        bank: formData.bank,
        accountNumber: formData.accountNumber,
        accountHolderName: formData.accountHolderName,
        amount: formData.amount,
        description: formData.description,
        currency: balance?.currType || 'USD',
      });

      if (response.success) {
        await refreshWallet(); // Refresh balance after successful transfer
        
        Alert.alert(
          'Bank Transfer Successful',
          `${FormattingUtils.formatCurrency(formData.amount, balance?.currType || 'USD')} has been transferred to ${formData.accountHolderName}!`,
          [
            {
              text: 'OK',
              onPress: () => router.push('/(main)/(tabs)/home'),
            },
          ]
        );
      } else {
        Alert.alert('Transfer Failed', response.error || 'Please try again.');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Transfer failed. Please try again.';
      Alert.alert('Transfer Failed', errorMessage);
    } finally {
      setIsProcessing(false);
    }
  }

  const handleGoBack = () => {
    router.back();
  };

  const transferAmount = values.amount ? parseFloat(values.amount) : 0;
  const fee = transferAmount * 0.01; // 1% fee
  const totalAmount = transferAmount + fee;
  const hasInsufficientFunds = totalAmount > (balance ? parseFloat(balance.balance) : 0);
  const selectedBank = banks.find(bank => bank.id === values.bank);

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1">
        {/* Header */}
        <HStack className="justify-between items-center p-4 bg-white border-b border-gray-200">
          <Pressable onPress={handleGoBack}>
            <Box className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center">
              <Text className="text-gray-600 text-lg">←</Text>
            </Box>
          </Pressable>
          
          <Text className="text-lg font-semibold text-gray-800">Bank Transfer</Text>
          
          <Box className="w-10 h-10" />
        </HStack>

        <ScrollView className="flex-1">
          <VStack className="p-6" space="lg">
            {/* Balance Display */}
            <Card className="bg-white">
              <VStack className="p-4" space="sm">
                <Text className="text-gray-600 text-sm">Available Balance</Text>
                <Text className="text-2xl font-bold text-gray-800">
                  {balance ? FormattingUtils.formatCurrency(balance.balance, balance.currType) : '---'}
                </Text>
              </VStack>
            </Card>

            {/* Transfer Form */}
            <VStack space="md">
              <FormControl isInvalid={!!errors.bank}>
                <FormControlLabel>
                  <FormControlLabelText>Select Bank</FormControlLabelText>
                </FormControlLabel>
                <Select
                  selectedValue={values.bank}
                  onValueChange={(value) => setValue('bank', value)}
                >
                  <SelectTrigger>
                    <SelectInput placeholder="Choose bank" />
                    <SelectIcon as={ChevronDownIcon} />
                  </SelectTrigger>
                  <SelectPortal>
                    <SelectBackdrop />
                    <SelectContent>
                      <SelectDragIndicatorWrapper>
                        <SelectDragIndicator />
                      </SelectDragIndicatorWrapper>
                      {banks.map((bank) => (
                        <SelectItem
                          key={bank.id}
                          label={bank.name}
                          value={bank.id}
                        />
                      ))}
                    </SelectContent>
                  </SelectPortal>
                </Select>
                <FormControlError>
                  <FormControlErrorText>{errors.bank}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.accountNumber}>
                <FormControlLabel>
                  <FormControlLabelText>Account Number</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="Enter account number"
                    value={values.accountNumber}
                    onChangeText={(text) => setValue('accountNumber', text)}
                    keyboardType="numeric"
                    onBlur={() => validate('accountNumber')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.accountNumber}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.accountHolderName}>
                <FormControlLabel>
                  <FormControlLabelText>Account Holder Name</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="Enter account holder name"
                    value={values.accountHolderName}
                    onChangeText={(text) => setValue('accountHolderName', text)}
                    autoCapitalize="words"
                    onBlur={() => validate('accountHolderName')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.accountHolderName}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.amount || hasInsufficientFunds}>
                <FormControlLabel>
                  <FormControlLabelText>Amount</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="0.00"
                    value={values.amount}
                    onChangeText={(text) => setValue('amount', text)}
                    keyboardType="decimal-pad"
                    onBlur={() => validate('amount')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>
                    {errors.amount || (hasInsufficientFunds ? 'Insufficient funds including fees' : '')}
                  </FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.description}>
                <FormControlLabel>
                  <FormControlLabelText>Description</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="What's this transfer for?"
                    value={values.description}
                    onChangeText={(text) => setValue('description', text)}
                    onBlur={() => validate('description')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.description}</FormControlErrorText>
                </FormControlError>
              </FormControl>
            </VStack>

            {/* Transfer Summary */}
            {values.amount && values.bank && values.accountHolderName && (
              <Card className="bg-gray-50">
                <VStack className="p-4" space="sm">
                  <Text className="font-semibold text-gray-800">Transfer Summary</Text>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Bank</Text>
                    <Text className="font-medium">{selectedBank?.name}</Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Account Holder</Text>
                    <Text className="font-medium">{values.accountHolderName}</Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Account Number</Text>
                    <Text className="font-medium">
                      {FormattingUtils.formatAccountNumber(values.accountNumber)}
                    </Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Amount</Text>
                    <Text className="font-medium">
                      {FormattingUtils.formatCurrency(values.amount, balance?.currType || 'USD')}
                    </Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Transfer Fee (1%)</Text>
                    <Text className="font-medium">
                      {FormattingUtils.formatCurrency(fee.toString(), balance?.currType || 'USD')}
                    </Text>
                  </HStack>
                  <Divider />
                  <HStack className="justify-between">
                    <Text className="font-semibold text-gray-800">Total Deduction</Text>
                    <Text className="font-bold text-lg">
                      {FormattingUtils.formatCurrency(totalAmount.toString(), balance?.currType || 'USD')}
                    </Text>
                  </HStack>
                </VStack>
              </Card>
            )}

            {/* Submit Button */}
            <Button
              onPress={handleSubmit}
              isDisabled={isProcessing || !isValid || hasInsufficientFunds}
              className="mt-4"
            >
              <ButtonText>
                {isProcessing ? 'Processing...' : 'Transfer to Bank'}
              </ButtonText>
            </Button>

            {/* Processing Time Notice */}
            <Card className="bg-yellow-50 border-yellow-200">
              <HStack className="p-4 items-center" space="md">
                <Text className="text-yellow-600 text-xl">⏰</Text>
                <VStack className="flex-1">
                  <Text className="text-yellow-800 font-medium text-sm">Processing Time</Text>
                  <Text className="text-yellow-700 text-xs">
                    Bank transfers may take 1-3 business days to process
                  </Text>
                </VStack>
              </HStack>
            </Card>
          </VStack>
        </ScrollView>
      </KeyboardAvoidingView>

      {isProcessing && (
        <LoadingSpinner message="Processing bank transfer..." overlay />
      )}
    </SafeAreaView>
  );
}
