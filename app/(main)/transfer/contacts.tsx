import React, { useState, useEffect } from 'react';
import { Alert, Platform } from 'react-native';
import { router } from 'expo-router';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonText,
  Input,
  InputField,
  SafeAreaView,
  ScrollView,
  Pressable,
  Card,
  Avatar,
  AvatarFallbackText,
  Divider,
} from '@/components/ui';
import { StorageUtils } from '@/src/utils/storage';
import { FormattingUtils } from '@/src/utils/formatting';
import { LoadingSpinner } from '@/src/components/LoadingSpinner';
import { EmptyState } from '@/src/components/EmptyState';
import { useDebounce } from '@/src/hooks/useDebounce';

interface Contact {
  id: string;
  name: string;
  phoneNumber: string;
  profileImage?: string;
  isFavorite: boolean;
  lastTransactionDate?: string;
}

export default function ContactsScreen() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState<'all' | 'favorites' | 'recent'>('all');

  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  useEffect(() => {
    loadContacts();
  }, []);

  useEffect(() => {
    filterContacts();
  }, [contacts, debouncedSearchQuery, selectedTab]);

  const loadContacts = async () => {
    try {
      setIsLoading(true);
      const recentContacts = await StorageUtils.getRecentContacts();
      setContacts(recentContacts);
    } catch (error) {
      console.error('Failed to load contacts:', error);
      Alert.alert('Error', 'Failed to load contacts');
    } finally {
      setIsLoading(false);
    }
  };

  const filterContacts = () => {
    let filtered = [...contacts];

    // Filter by tab
    switch (selectedTab) {
      case 'favorites':
        filtered = filtered.filter(contact => contact.isFavorite);
        break;
      case 'recent':
        filtered = filtered.filter(contact => contact.lastTransactionDate);
        filtered.sort((a, b) => {
          const dateA = new Date(a.lastTransactionDate || 0).getTime();
          const dateB = new Date(b.lastTransactionDate || 0).getTime();
          return dateB - dateA;
        });
        break;
      default:
        // All contacts
        break;
    }

    // Filter by search query
    if (debouncedSearchQuery) {
      filtered = filtered.filter(contact =>
        contact.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        contact.phoneNumber.includes(debouncedSearchQuery)
      );
    }

    setFilteredContacts(filtered);
  };

  const handleContactSelect = (contact: Contact) => {
    router.push({
      pathname: '/(main)/transfer/send-money',
      params: { 
        contactId: contact.id,
        contactName: contact.name,
        contactPhone: contact.phoneNumber,
      },
    });
  };

  const toggleFavorite = async (contact: Contact) => {
    try {
      const updatedContact = { ...contact, isFavorite: !contact.isFavorite };
      
      // Update in storage
      const allContacts = await StorageUtils.getRecentContacts();
      const updatedContacts = allContacts.map(c => 
        c.id === contact.id ? updatedContact : c
      );
      await StorageUtils.setRecentContacts(updatedContacts);
      
      // Update local state
      setContacts(updatedContacts);
      
    } catch (error) {
      console.error('Failed to update favorite:', error);
      Alert.alert('Error', 'Failed to update favorite');
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleAddContact = () => {
    Alert.alert('Add Contact', 'Contact management will be available soon!');
  };

  const tabs = [
    { id: 'all', label: 'All', count: contacts.length },
    { id: 'favorites', label: 'Favorites', count: contacts.filter(c => c.isFavorite).length },
    { id: 'recent', label: 'Recent', count: contacts.filter(c => c.lastTransactionDate).length },
  ];

  if (isLoading) {
    return <LoadingSpinner message="Loading contacts..." />;
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Header */}
      <HStack className="justify-between items-center p-4 bg-white border-b border-gray-200">
        <Pressable onPress={handleGoBack}>
          <Box className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center">
            <Text className="text-gray-600 text-lg">←</Text>
          </Box>
        </Pressable>
        
        <Text className="text-lg font-semibold text-gray-800">Contacts</Text>
        
        <Pressable onPress={handleAddContact}>
          <Box className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center">
            <Text className="text-blue-600 text-lg">+</Text>
          </Box>
        </Pressable>
      </HStack>

      <VStack className="flex-1">
        {/* Search Bar */}
        <Box className="p-4 bg-white border-b border-gray-200">
          <Input>
            <InputField
              placeholder="Search contacts..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoCapitalize="none"
            />
          </Input>
        </Box>

        {/* Tabs */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} className="bg-white border-b border-gray-200">
          <HStack className="px-4 py-2" space="md">
            {tabs.map((tab) => (
              <Pressable
                key={tab.id}
                onPress={() => setSelectedTab(tab.id as any)}
                className={`px-4 py-2 rounded-full ${
                  selectedTab === tab.id
                    ? 'bg-blue-600'
                    : 'bg-gray-100'
                }`}
              >
                <HStack className="items-center" space="xs">
                  <Text
                    className={`text-sm font-medium ${
                      selectedTab === tab.id ? 'text-white' : 'text-gray-600'
                    }`}
                  >
                    {tab.label}
                  </Text>
                  <Box
                    className={`px-2 py-1 rounded-full ${
                      selectedTab === tab.id ? 'bg-white/20' : 'bg-gray-200'
                    }`}
                  >
                    <Text
                      className={`text-xs ${
                        selectedTab === tab.id ? 'text-white' : 'text-gray-600'
                      }`}
                    >
                      {tab.count}
                    </Text>
                  </Box>
                </HStack>
              </Pressable>
            ))}
          </HStack>
        </ScrollView>

        {/* Contacts List */}
        <ScrollView className="flex-1">
          {filteredContacts.length > 0 ? (
            <VStack className="p-4" space="sm">
              {filteredContacts.map((contact, index) => (
                <VStack key={contact.id}>
                  <Pressable onPress={() => handleContactSelect(contact)}>
                    <Card className="bg-white">
                      <HStack className="p-4 items-center justify-between">
                        <HStack className="items-center flex-1" space="md">
                          <Avatar size="md">
                            <AvatarFallbackText>
                              {contact.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallbackText>
                          </Avatar>
                          
                          <VStack className="flex-1">
                            <Text className="font-medium text-gray-800">
                              {contact.name}
                            </Text>
                            <Text className="text-sm text-gray-600">
                              {FormattingUtils.formatPhoneNumber(contact.phoneNumber)}
                            </Text>
                            {contact.lastTransactionDate && (
                              <Text className="text-xs text-gray-500">
                                Last transfer: {FormattingUtils.formatRelativeTime(contact.lastTransactionDate)}
                              </Text>
                            )}
                          </VStack>
                        </HStack>

                        <HStack className="items-center" space="sm">
                          <Pressable onPress={() => toggleFavorite(contact)}>
                            <Box className="w-10 h-10 items-center justify-center">
                              <Text className="text-lg">
                                {contact.isFavorite ? '❤️' : '🤍'}
                              </Text>
                            </Box>
                          </Pressable>
                          
                          <Box className="w-6 h-6 items-center justify-center">
                            <Text className="text-gray-400">›</Text>
                          </Box>
                        </HStack>
                      </HStack>
                    </Card>
                  </Pressable>
                  {index < filteredContacts.length - 1 && <Divider />}
                </VStack>
              ))}
            </VStack>
          ) : (
            <EmptyState
              icon={searchQuery ? '🔍' : selectedTab === 'favorites' ? '❤️' : '👥'}
              title={
                searchQuery 
                  ? 'No contacts found'
                  : selectedTab === 'favorites'
                  ? 'No favorite contacts'
                  : selectedTab === 'recent'
                  ? 'No recent transfers'
                  : 'No contacts yet'
              }
              description={
                searchQuery
                  ? 'Try adjusting your search terms'
                  : selectedTab === 'favorites'
                  ? 'Mark contacts as favorites for quick access'
                  : selectedTab === 'recent'
                  ? 'Contacts you transfer money to will appear here'
                  : 'Start sending money to build your contact list'
              }
              actionText={!searchQuery ? 'Send Money' : undefined}
              onAction={!searchQuery ? () => router.push('/(main)/transfer/send-money') : undefined}
            />
          )}
        </ScrollView>
      </VStack>
    </SafeAreaView>
  );
}
