import React, { useState, useEffect } from 'react';
import { Al<PERSON>, Platform } from 'react-native';
import { router } from 'expo-router';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonText,
  Input,
  InputField,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  SafeAreaView,
  KeyboardAvoidingView,
  ScrollView,
  Pressable,
  Card,
  Avatar,
  AvatarFallbackText,
  Divider,
} from '@/components/ui';
import { useWallet } from '@/src/stores/walletStore';
import { useForm } from '@/src/hooks/useForm';
import { ValidationUtils } from '@/src/utils/validation';
import { FormattingUtils } from '@/src/utils/formatting';
import { StorageUtils } from '@/src/utils/storage';
import { LoadingSpinner } from '@/src/components/LoadingSpinner';
import { transferApi } from '@/src/services/api';

interface TransferFormData {
  recipient: string;
  amount: string;
  description: string;
}

interface Contact {
  id: string;
  name: string;
  phoneNumber: string;
  profileImage?: string;
  isFavorite: boolean;
  lastTransactionDate?: string;
}

export default function SendMoneyScreen() {
  const { balance, refreshWallet } = useWallet();
  const [isProcessing, setIsProcessing] = useState(false);
  const [recentContacts, setRecentContacts] = useState<Contact[]>([]);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);

  const {
    values,
    errors,
    isValid,
    setValue,
    validate,
    handleSubmit,
  } = useForm<TransferFormData>({
    initialValues: {
      recipient: '',
      amount: '',
      description: '',
    },
    validationRules: {
      recipient: ValidationUtils.validatePhoneNumber,
      amount: ValidationUtils.validateAmount,
      description: (value) => ValidationUtils.validateRequired(value, 'Description'),
    },
    onSubmit: handleTransfer,
  });

  useEffect(() => {
    loadRecentContacts();
  }, []);

  const loadRecentContacts = async () => {
    try {
      const contacts = await StorageUtils.getRecentContacts();
      setRecentContacts(contacts.slice(0, 5)); // Show only 5 recent contacts
    } catch (error) {
      console.error('Failed to load recent contacts:', error);
    }
  };

  async function handleTransfer(formData: TransferFormData) {
    // Check if user has sufficient balance
    if (balance && parseFloat(formData.amount) > parseFloat(balance.balance)) {
      Alert.alert('Insufficient Funds', 'You do not have enough balance for this transfer.');
      return;
    }

    setIsProcessing(true);

    try {
      const response = await transferApi.sendMoney({
        recipient: formData.recipient,
        amount: formData.amount,
        description: formData.description,
        currency: balance?.currType || 'USD',
      });

      if (response.success) {
        // Save contact to recent contacts
        if (selectedContact) {
          await StorageUtils.addRecentContact({
            ...selectedContact,
            lastTransactionDate: new Date().toISOString(),
          });
        } else {
          // Create new contact from phone number
          await StorageUtils.addRecentContact({
            id: Date.now().toString(),
            name: formData.recipient,
            phoneNumber: formData.recipient,
            isFavorite: false,
            lastTransactionDate: new Date().toISOString(),
          });
        }

        await refreshWallet(); // Refresh balance after successful transfer
        
        Alert.alert(
          'Transfer Successful',
          `${FormattingUtils.formatCurrency(formData.amount, balance?.currType || 'USD')} has been sent to ${formData.recipient}!`,
          [
            {
              text: 'OK',
              onPress: () => router.push('/(main)/(tabs)/home'),
            },
          ]
        );
      } else {
        Alert.alert('Transfer Failed', response.error || 'Please try again.');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Transfer failed. Please try again.';
      Alert.alert('Transfer Failed', errorMessage);
    } finally {
      setIsProcessing(false);
    }
  }

  const handleContactSelect = (contact: Contact) => {
    setSelectedContact(contact);
    setValue('recipient', contact.phoneNumber);
  };

  const handleViewAllContacts = () => {
    router.push('/(main)/transfer/contacts');
  };

  const handleGoBack = () => {
    router.back();
  };

  const transferAmount = values.amount ? parseFloat(values.amount) : 0;
  const hasInsufficientFunds = transferAmount > (balance ? parseFloat(balance.balance) : 0);

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1">
        {/* Header */}
        <HStack className="justify-between items-center p-4 bg-white border-b border-gray-200">
          <Pressable onPress={handleGoBack}>
            <Box className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center">
              <Text className="text-gray-600 text-lg">←</Text>
            </Box>
          </Pressable>
          
          <Text className="text-lg font-semibold text-gray-800">Send Money</Text>
          
          <Pressable onPress={handleViewAllContacts}>
            <Box className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center">
              <Text className="text-blue-600 text-lg">👥</Text>
            </Box>
          </Pressable>
        </HStack>

        <ScrollView className="flex-1">
          <VStack className="p-6" space="lg">
            {/* Balance Display */}
            <Card className="bg-white">
              <VStack className="p-4" space="sm">
                <Text className="text-gray-600 text-sm">Available Balance</Text>
                <Text className="text-2xl font-bold text-gray-800">
                  {balance ? FormattingUtils.formatCurrency(balance.balance, balance.currType) : '---'}
                </Text>
              </VStack>
            </Card>

            {/* Recent Contacts */}
            {recentContacts.length > 0 && (
              <VStack space="md">
                <HStack className="justify-between items-center">
                  <Text className="text-lg font-semibold text-gray-800">Recent Contacts</Text>
                  <Pressable onPress={handleViewAllContacts}>
                    <Text className="text-blue-600 text-sm">View All</Text>
                  </Pressable>
                </HStack>
                
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <HStack space="md" className="px-2">
                    {recentContacts.map((contact) => (
                      <Pressable
                        key={contact.id}
                        onPress={() => handleContactSelect(contact)}
                        className={`items-center p-3 rounded-lg ${
                          selectedContact?.id === contact.id ? 'bg-blue-50' : 'bg-white'
                        }`}
                      >
                        <Avatar size="md" className="mb-2">
                          <AvatarFallbackText>
                            {contact.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallbackText>
                        </Avatar>
                        <Text className={`text-sm font-medium text-center ${
                          selectedContact?.id === contact.id ? 'text-blue-600' : 'text-gray-800'
                        }`}>
                          {contact.name.length > 10 ? contact.name.substring(0, 10) + '...' : contact.name}
                        </Text>
                        <Text className="text-xs text-gray-500 text-center">
                          {FormattingUtils.formatPhoneNumber(contact.phoneNumber)}
                        </Text>
                      </Pressable>
                    ))}
                  </HStack>
                </ScrollView>
              </VStack>
            )}

            {/* Transfer Form */}
            <VStack space="md">
              <FormControl isInvalid={!!errors.recipient}>
                <FormControlLabel>
                  <FormControlLabelText>Recipient Phone Number</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="Enter phone number"
                    value={values.recipient}
                    onChangeText={(text) => {
                      setValue('recipient', text);
                      setSelectedContact(null); // Clear selected contact when typing
                    }}
                    keyboardType="phone-pad"
                    onBlur={() => validate('recipient')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.recipient}</FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.amount || hasInsufficientFunds}>
                <FormControlLabel>
                  <FormControlLabelText>Amount</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="0.00"
                    value={values.amount}
                    onChangeText={(text) => setValue('amount', text)}
                    keyboardType="decimal-pad"
                    onBlur={() => validate('amount')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>
                    {errors.amount || (hasInsufficientFunds ? 'Insufficient funds' : '')}
                  </FormControlErrorText>
                </FormControlError>
              </FormControl>

              <FormControl isInvalid={!!errors.description}>
                <FormControlLabel>
                  <FormControlLabelText>Description</FormControlLabelText>
                </FormControlLabel>
                <Input>
                  <InputField
                    placeholder="What's this transfer for?"
                    value={values.description}
                    onChangeText={(text) => setValue('description', text)}
                    onBlur={() => validate('description')}
                  />
                </Input>
                <FormControlError>
                  <FormControlErrorText>{errors.description}</FormControlErrorText>
                </FormControlError>
              </FormControl>
            </VStack>

            {/* Transfer Summary */}
            {values.amount && values.recipient && (
              <Card className="bg-gray-50">
                <VStack className="p-4" space="sm">
                  <Text className="font-semibold text-gray-800">Transfer Summary</Text>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Recipient</Text>
                    <Text className="font-medium">
                      {selectedContact?.name || FormattingUtils.formatPhoneNumber(values.recipient)}
                    </Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Amount</Text>
                    <Text className="font-medium">
                      {FormattingUtils.formatCurrency(values.amount, balance?.currType || 'USD')}
                    </Text>
                  </HStack>
                  <HStack className="justify-between">
                    <Text className="text-gray-600">Transfer Fee</Text>
                    <Text className="font-medium">Free</Text>
                  </HStack>
                  <Divider />
                  <HStack className="justify-between">
                    <Text className="font-semibold text-gray-800">Total</Text>
                    <Text className="font-bold text-lg">
                      {FormattingUtils.formatCurrency(values.amount, balance?.currType || 'USD')}
                    </Text>
                  </HStack>
                </VStack>
              </Card>
            )}

            {/* Submit Button */}
            <Button
              onPress={handleSubmit}
              isDisabled={isProcessing || !isValid || hasInsufficientFunds}
              className="mt-4"
            >
              <ButtonText>
                {isProcessing ? 'Processing...' : 'Send Money'}
              </ButtonText>
            </Button>

            {/* Security Notice */}
            <Card className="bg-blue-50 border-blue-200">
              <HStack className="p-4 items-center" space="md">
                <Text className="text-blue-600 text-xl">🔒</Text>
                <VStack className="flex-1">
                  <Text className="text-blue-800 font-medium text-sm">Secure Transfer</Text>
                  <Text className="text-blue-700 text-xs">
                    All transfers are encrypted and processed securely
                  </Text>
                </VStack>
              </HStack>
            </Card>
          </VStack>
        </ScrollView>
      </KeyboardAvoidingView>

      {isProcessing && (
        <LoadingSpinner message="Processing transfer..." overlay />
      )}
    </SafeAreaView>
  );
}
