<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/OpenSSL.framework/Headers/OpenSSL.h</key>
		<data>
		TYMr1SKbMS+Pm94I583X46xmkDc=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/aes.h</key>
		<data>
		/QHXsfp5KZBtt0hpQ+PGhRB5TQE=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/asn1.h</key>
		<data>
		/JULnimbzTltoXNqIFMZvE15UFw=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/asn1err.h</key>
		<data>
		vClK4nFx8VUb19HBjU1cQuNfemY=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/asn1t.h</key>
		<data>
		LWrSPPRO1SZ6tiCyd9G2baHBT0g=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/async.h</key>
		<data>
		Du5KwAWX1O4Ld/G5CC+CgQkQU9M=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/asyncerr.h</key>
		<data>
		K5Jc+TuPuL/d4mP5tAhHKE2LRmc=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/bio.h</key>
		<data>
		3e8z1vvxgbXPwmeg+1bRSmgfCG8=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/bioerr.h</key>
		<data>
		N0rVDI9qQwyDEWvlqW1vGQrh0RU=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/blowfish.h</key>
		<data>
		BLqJpLWCl4Gl0DR4WO0luoyixMg=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/bn.h</key>
		<data>
		zwKCI0fcYjm7MT9ZsUzGG4BDLTE=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/bnerr.h</key>
		<data>
		ybn9OmsYdCvnSKGlXSQQJ8b+Y44=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/buffer.h</key>
		<data>
		70BHajzy0JGUYwo7F+dpD0CeWrs=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/buffererr.h</key>
		<data>
		PSVdu9joU+POeEegSPtAPctXWyI=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/camellia.h</key>
		<data>
		R0cxfQe4VMejfw/FBnV5jlrTxS8=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/cast.h</key>
		<data>
		tg9fweKyld2MF5c1iw7sEh5btDM=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/cmac.h</key>
		<data>
		SsfJcPvnO3RZ7i+QyWeuyIBoFr4=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/cms.h</key>
		<data>
		WhfyS38eVlM/0EjH+SRNVL1fhB0=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/cmserr.h</key>
		<data>
		icv8DehBtI8fn3XrZvJ8GaK3Nm8=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/comp.h</key>
		<data>
		iXVcuTWmFCf9QLEmp9sNP8JQUw0=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/comperr.h</key>
		<data>
		smXqglFOEgRt0CXetpBcXnWbZYI=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/conf.h</key>
		<data>
		lY422quRPGGoMtvoZu73RVfY38s=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/conf_api.h</key>
		<data>
		aEkI7MCNJGZ+SJxs514uMY1oW3s=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/conferr.h</key>
		<data>
		HhxJvI2jMpKEWKwpMC+m/3YD5tE=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/crypto.h</key>
		<data>
		9mlWjKYcqA63rFf05YOE/OnlLlE=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/cryptoerr.h</key>
		<data>
		rvUn24W+SZM/y6GTW6NG1DW8KQo=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/ct.h</key>
		<data>
		cpeLfPNApLUZ414BRmIcahaW0y0=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/cterr.h</key>
		<data>
		z6hnmIDS0tby0u7XWscijJOnipQ=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/des.h</key>
		<data>
		znOw/0Vq2B1QWQ5QlySAEIkrdwE=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/dh.h</key>
		<data>
		9zO15LRHFl5v1MTrCuS3Q2iGGu0=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/dherr.h</key>
		<data>
		PTwLUzp4LhfTwc/VMpRyT9YTuRU=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/dsa.h</key>
		<data>
		AEYyYVu2fy7yPpiyRyjyypHNCKI=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/dsaerr.h</key>
		<data>
		jmOqsG1TSs1yTIm74Qstlz/KODg=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/dtls1.h</key>
		<data>
		fDDC+ixg7o1tDB4basD847J6+Zk=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/e_os2.h</key>
		<data>
		lavqS62GkOuhGihyu+WOjVd92SM=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/ebcdic.h</key>
		<data>
		z5Fn9TbPaQo8zoY+Uwo/lSr9SJ8=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/ec.h</key>
		<data>
		VDanIs80uH9tNKL+PSMmYL0YIu4=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/ecdh.h</key>
		<data>
		iXUqwjlcXCix2i1+T/zXRV4PU18=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/ecdsa.h</key>
		<data>
		iXUqwjlcXCix2i1+T/zXRV4PU18=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/ecerr.h</key>
		<data>
		GyM9ZofjjCqJIe+IbO9j4p+HDZk=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/engine.h</key>
		<data>
		dS4eXHBYjCoSL3nDXCYuyZR9V9c=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/engineerr.h</key>
		<data>
		H6oGO47GeHqzTYa913WPlF5cZzQ=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/err.h</key>
		<data>
		OZsmLZ7UryVhV7QY17J5cymnWlY=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/evp.h</key>
		<data>
		Ue0l0jetFcNRP+2k4ve5l9kWsOw=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/evperr.h</key>
		<data>
		YPfW2qvVYScvnOl+vKfUlzf4pvg=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/hmac.h</key>
		<data>
		oNXOm72CsIiR3i2/7dzmPfXHpnk=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/idea.h</key>
		<data>
		9OhfGjNERiWm+IaFZng3mj74a70=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/kdf.h</key>
		<data>
		8bAA2Z5x72kQuKB9lY8B07YpOvA=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/kdferr.h</key>
		<data>
		eBl7XkjAByV3fh1j+2ZSsSVdPd0=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/lhash.h</key>
		<data>
		gsp3p9sYfgyLm8w777aVi7LZzaA=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/md2.h</key>
		<data>
		SU5g+hFH8KXJwSElUE6qnz88XbQ=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/md4.h</key>
		<data>
		NVmYVdXaFSHylpRJRh52LUqSAIY=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/md5.h</key>
		<data>
		8R2dids4HGec0BuJ5RjnI0sNAqs=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/mdc2.h</key>
		<data>
		ceP5kO5gOJDJGS7HrDRjpWWG2i4=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/modes.h</key>
		<data>
		oykGDYKdMkpM+dh+C5148xyIG9A=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/obj_mac.h</key>
		<data>
		0xczM7tqfkjs0KVU5TgD3MmB0O8=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/objects.h</key>
		<data>
		1BKkzZQ86VAQYSD2wS3W+kAe8lU=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/objectserr.h</key>
		<data>
		62CLZz09xeqGq4nUpt1HfuvRrbM=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/ocsp.h</key>
		<data>
		/vPykCbZnxZ/oD4x73y3vCFlfAg=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/ocsperr.h</key>
		<data>
		HzVTI0XxPNFGeTAYcha32Bf1qLw=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/opensslconf.h</key>
		<data>
		JcwLt3wVdxf0h0NZ9jm1E8n98Z8=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/opensslv.h</key>
		<data>
		RV0inZj+UfBzOwFBUF97hvsSAjg=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/ossl_typ.h</key>
		<data>
		/hD+IfNYoPQpFE2wIl8jHi6BRVI=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/pem.h</key>
		<data>
		32ly0pNLPzdTYGECzCbGY6huGuw=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/pem2.h</key>
		<data>
		t2jj9ipWiYEJZvgm7/w68FCv7xk=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/pemerr.h</key>
		<data>
		LPK/t/8oy0h9S1TUlxQnvG640ps=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/pkcs12.h</key>
		<data>
		Wgtt5Ajf2I0Qe0Yw/C6/da673Vs=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/pkcs12err.h</key>
		<data>
		iYGY/k8EiROz+H6/d3nIzlOnXHY=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/pkcs7.h</key>
		<data>
		DSVKNOqJWVpNEj9g1afFP/AAHbs=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/pkcs7err.h</key>
		<data>
		vbVkdgXZDNlmUBjHsDcvjoyWNTE=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/rand.h</key>
		<data>
		BUI/kpuBobNUPlXrW/OkHeq9ClM=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/rand_drbg.h</key>
		<data>
		dZJZsnraib5Oxt12qSJ4bXxP3/w=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/randerr.h</key>
		<data>
		/b3o6sjMLxkewJmLW7LeYF/10A0=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/rc2.h</key>
		<data>
		Xzwvx1iv4W35klxWCpyRR35/Uwc=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/rc4.h</key>
		<data>
		01mH39v8pvXIdzB3N/3bn0uJwVs=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/rc5.h</key>
		<data>
		nVMdNFdbOheiSzNQjJ5v92LvEmI=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/ripemd.h</key>
		<data>
		UUNVXGUU1UnsGpXivIzpc/ZyFQs=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/rsa.h</key>
		<data>
		BXOED7wh3elZhA9q8KoFrXffhUg=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/rsaerr.h</key>
		<data>
		jrv7NJXCaI3Ppho9VNnyZFn9jxQ=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/safestack.h</key>
		<data>
		Cd9WYSUSt9OnXah9XHF3QevCc+E=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/seed.h</key>
		<data>
		xIqmrORS9667meS1VHouBNO1FGI=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/sha.h</key>
		<data>
		lu1HA4odImsyOAN6vcDKaHOxMrc=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/shim.h</key>
		<data>
		sMxnt7+rU4Gir29aFtcGAwjCdhs=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/sm2.h</key>
		<data>
		J9BR3kk+bjp4DUXqRy3dTOnScEM=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/sm3.h</key>
		<data>
		H8hFoehBM7TTiupNtJpraPdF6TE=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/sm4.h</key>
		<data>
		u11NBly6iFRmqITEgK0HhoQVACA=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/srp.h</key>
		<data>
		hs0FaZQgqkGx2ATJtkZLR3E7ajQ=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/srtp.h</key>
		<data>
		Xiev9lzpqvNNo1eXz+UZl4RZh+0=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/ssl.h</key>
		<data>
		mhalYjN/qeuEF+l6WIVquvXvjOs=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/ssl2.h</key>
		<data>
		ZUBF9zzQr/EnSx9hH0JCB0ETP8s=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/ssl3.h</key>
		<data>
		cA/eJmiVcOy62x5KksvM5fafytI=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/sslerr.h</key>
		<data>
		4EiOgpXMaHQuyZqwDCHkMoCYXhw=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/stack.h</key>
		<data>
		HlhyAUEwF7fsEBjOpYfB+ZGw2SU=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/store.h</key>
		<data>
		f3f0erlSosQY7bDqrHLGzKe7YY4=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/storeerr.h</key>
		<data>
		kv/pt/4V9S0com6azZDBL4S2ef4=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/symhacks.h</key>
		<data>
		9immsicIban6k2s+1MF7N3ITwzE=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/tls1.h</key>
		<data>
		snV9T38ogNzmGbcPr9Rn32lFHOo=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/ts.h</key>
		<data>
		iWDV/jVLCzacuMrj1s833l3wAYY=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/tserr.h</key>
		<data>
		PI3IsQNlWi/RwHMUbbL1VJDUvj0=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/txt_db.h</key>
		<data>
		NoTeslBGWtynVU+GDhyxyzXUdTE=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/ui.h</key>
		<data>
		++9liaOBXfJY40d+p4saEce2zpw=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/uierr.h</key>
		<data>
		R61EyGTilaauZmnHry4jjFiN5ko=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/whrlpool.h</key>
		<data>
		Uo0K/dGVqhsRUor/8CFpmWNaoHY=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/x509.h</key>
		<data>
		5wWbXaF8/6/pZR0i8O64cxA2vYY=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/x509_vfy.h</key>
		<data>
		+5MyYNetMS2YX3CRFqSzMEZc4TE=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/x509err.h</key>
		<data>
		z4/1nUkbgTSuqRKSBA/+tS9zTD4=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/x509v3.h</key>
		<data>
		YhR8NOnXe50FuvmDc73j2oiOZGo=
		</data>
		<key>ios-arm64/OpenSSL.framework/Headers/x509v3err.h</key>
		<data>
		X1T6LneJ+WEeudGjNgNYP08RqMU=
		</data>
		<key>ios-arm64/OpenSSL.framework/Info.plist</key>
		<data>
		7SpMbr9ijEs9WZlUGf6R9fV2Rtw=
		</data>
		<key>ios-arm64/OpenSSL.framework/Modules/module.modulemap</key>
		<data>
		wSMjVnQJnXCQkrl0p+m5ijRHe3c=
		</data>
		<key>ios-arm64/OpenSSL.framework/OpenSSL</key>
		<data>
		uQAtzpTpeldYNEC6h3npAoL0V/U=
		</data>
		<key>ios-arm64/OpenSSL.framework/PrivacyInfo.xcprivacy</key>
		<data>
		AL1dh5ctObXBjoBiabSJ86M3HQs=
		</data>
		<key>ios-arm64/OpenSSL.framework/_CodeSignature/CodeDirectory</key>
		<data>
		K2HAhfB7lo2i+OrS08dDRvaWQEA=
		</data>
		<key>ios-arm64/OpenSSL.framework/_CodeSignature/CodeRequirements</key>
		<data>
		LivNFy9yoqb1NZc0y0RjgYMqdYM=
		</data>
		<key>ios-arm64/OpenSSL.framework/_CodeSignature/CodeRequirements-1</key>
		<data>
		Kx5DIwGCOLbNQi3YdYRLynwdXFA=
		</data>
		<key>ios-arm64/OpenSSL.framework/_CodeSignature/CodeResources</key>
		<data>
		dhiboUceMhmHyBtiqyp+lM+rBWI=
		</data>
		<key>ios-arm64/OpenSSL.framework/_CodeSignature/CodeSignature</key>
		<data>
		AXBJap1yP/cLEqpaWWhiD10TFuQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/OpenSSL.h</key>
		<data>
		TYMr1SKbMS+Pm94I583X46xmkDc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/aes.h</key>
		<data>
		/QHXsfp5KZBtt0hpQ+PGhRB5TQE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/asn1.h</key>
		<data>
		/JULnimbzTltoXNqIFMZvE15UFw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/asn1err.h</key>
		<data>
		vClK4nFx8VUb19HBjU1cQuNfemY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/asn1t.h</key>
		<data>
		LWrSPPRO1SZ6tiCyd9G2baHBT0g=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/async.h</key>
		<data>
		Du5KwAWX1O4Ld/G5CC+CgQkQU9M=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/asyncerr.h</key>
		<data>
		K5Jc+TuPuL/d4mP5tAhHKE2LRmc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/bio.h</key>
		<data>
		3e8z1vvxgbXPwmeg+1bRSmgfCG8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/bioerr.h</key>
		<data>
		N0rVDI9qQwyDEWvlqW1vGQrh0RU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/blowfish.h</key>
		<data>
		BLqJpLWCl4Gl0DR4WO0luoyixMg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/bn.h</key>
		<data>
		zwKCI0fcYjm7MT9ZsUzGG4BDLTE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/bnerr.h</key>
		<data>
		ybn9OmsYdCvnSKGlXSQQJ8b+Y44=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/buffer.h</key>
		<data>
		70BHajzy0JGUYwo7F+dpD0CeWrs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/buffererr.h</key>
		<data>
		PSVdu9joU+POeEegSPtAPctXWyI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/camellia.h</key>
		<data>
		R0cxfQe4VMejfw/FBnV5jlrTxS8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/cast.h</key>
		<data>
		tg9fweKyld2MF5c1iw7sEh5btDM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/cmac.h</key>
		<data>
		SsfJcPvnO3RZ7i+QyWeuyIBoFr4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/cms.h</key>
		<data>
		WhfyS38eVlM/0EjH+SRNVL1fhB0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/cmserr.h</key>
		<data>
		icv8DehBtI8fn3XrZvJ8GaK3Nm8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/comp.h</key>
		<data>
		iXVcuTWmFCf9QLEmp9sNP8JQUw0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/comperr.h</key>
		<data>
		smXqglFOEgRt0CXetpBcXnWbZYI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/conf.h</key>
		<data>
		lY422quRPGGoMtvoZu73RVfY38s=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/conf_api.h</key>
		<data>
		aEkI7MCNJGZ+SJxs514uMY1oW3s=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/conferr.h</key>
		<data>
		HhxJvI2jMpKEWKwpMC+m/3YD5tE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/crypto.h</key>
		<data>
		9mlWjKYcqA63rFf05YOE/OnlLlE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/cryptoerr.h</key>
		<data>
		rvUn24W+SZM/y6GTW6NG1DW8KQo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ct.h</key>
		<data>
		cpeLfPNApLUZ414BRmIcahaW0y0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/cterr.h</key>
		<data>
		z6hnmIDS0tby0u7XWscijJOnipQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/des.h</key>
		<data>
		znOw/0Vq2B1QWQ5QlySAEIkrdwE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/dh.h</key>
		<data>
		9zO15LRHFl5v1MTrCuS3Q2iGGu0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/dherr.h</key>
		<data>
		PTwLUzp4LhfTwc/VMpRyT9YTuRU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/dsa.h</key>
		<data>
		AEYyYVu2fy7yPpiyRyjyypHNCKI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/dsaerr.h</key>
		<data>
		jmOqsG1TSs1yTIm74Qstlz/KODg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/dtls1.h</key>
		<data>
		fDDC+ixg7o1tDB4basD847J6+Zk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/e_os2.h</key>
		<data>
		lavqS62GkOuhGihyu+WOjVd92SM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ebcdic.h</key>
		<data>
		z5Fn9TbPaQo8zoY+Uwo/lSr9SJ8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ec.h</key>
		<data>
		VDanIs80uH9tNKL+PSMmYL0YIu4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ecdh.h</key>
		<data>
		iXUqwjlcXCix2i1+T/zXRV4PU18=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ecdsa.h</key>
		<data>
		iXUqwjlcXCix2i1+T/zXRV4PU18=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ecerr.h</key>
		<data>
		GyM9ZofjjCqJIe+IbO9j4p+HDZk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/engine.h</key>
		<data>
		dS4eXHBYjCoSL3nDXCYuyZR9V9c=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/engineerr.h</key>
		<data>
		H6oGO47GeHqzTYa913WPlF5cZzQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/err.h</key>
		<data>
		OZsmLZ7UryVhV7QY17J5cymnWlY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/evp.h</key>
		<data>
		Ue0l0jetFcNRP+2k4ve5l9kWsOw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/evperr.h</key>
		<data>
		YPfW2qvVYScvnOl+vKfUlzf4pvg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/hmac.h</key>
		<data>
		oNXOm72CsIiR3i2/7dzmPfXHpnk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/idea.h</key>
		<data>
		9OhfGjNERiWm+IaFZng3mj74a70=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/kdf.h</key>
		<data>
		8bAA2Z5x72kQuKB9lY8B07YpOvA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/kdferr.h</key>
		<data>
		eBl7XkjAByV3fh1j+2ZSsSVdPd0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/lhash.h</key>
		<data>
		gsp3p9sYfgyLm8w777aVi7LZzaA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/md2.h</key>
		<data>
		SU5g+hFH8KXJwSElUE6qnz88XbQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/md4.h</key>
		<data>
		NVmYVdXaFSHylpRJRh52LUqSAIY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/md5.h</key>
		<data>
		8R2dids4HGec0BuJ5RjnI0sNAqs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/mdc2.h</key>
		<data>
		ceP5kO5gOJDJGS7HrDRjpWWG2i4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/modes.h</key>
		<data>
		oykGDYKdMkpM+dh+C5148xyIG9A=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/obj_mac.h</key>
		<data>
		0xczM7tqfkjs0KVU5TgD3MmB0O8=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/objects.h</key>
		<data>
		1BKkzZQ86VAQYSD2wS3W+kAe8lU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/objectserr.h</key>
		<data>
		62CLZz09xeqGq4nUpt1HfuvRrbM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ocsp.h</key>
		<data>
		/vPykCbZnxZ/oD4x73y3vCFlfAg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ocsperr.h</key>
		<data>
		HzVTI0XxPNFGeTAYcha32Bf1qLw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/opensslconf.h</key>
		<data>
		yNGszHsg1rO96Mwyt+XRNUDMH3E=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/opensslv.h</key>
		<data>
		RV0inZj+UfBzOwFBUF97hvsSAjg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ossl_typ.h</key>
		<data>
		/hD+IfNYoPQpFE2wIl8jHi6BRVI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/pem.h</key>
		<data>
		32ly0pNLPzdTYGECzCbGY6huGuw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/pem2.h</key>
		<data>
		t2jj9ipWiYEJZvgm7/w68FCv7xk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/pemerr.h</key>
		<data>
		LPK/t/8oy0h9S1TUlxQnvG640ps=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/pkcs12.h</key>
		<data>
		Wgtt5Ajf2I0Qe0Yw/C6/da673Vs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/pkcs12err.h</key>
		<data>
		iYGY/k8EiROz+H6/d3nIzlOnXHY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/pkcs7.h</key>
		<data>
		DSVKNOqJWVpNEj9g1afFP/AAHbs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/pkcs7err.h</key>
		<data>
		vbVkdgXZDNlmUBjHsDcvjoyWNTE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/rand.h</key>
		<data>
		BUI/kpuBobNUPlXrW/OkHeq9ClM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/rand_drbg.h</key>
		<data>
		dZJZsnraib5Oxt12qSJ4bXxP3/w=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/randerr.h</key>
		<data>
		/b3o6sjMLxkewJmLW7LeYF/10A0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/rc2.h</key>
		<data>
		Xzwvx1iv4W35klxWCpyRR35/Uwc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/rc4.h</key>
		<data>
		01mH39v8pvXIdzB3N/3bn0uJwVs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/rc5.h</key>
		<data>
		nVMdNFdbOheiSzNQjJ5v92LvEmI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ripemd.h</key>
		<data>
		UUNVXGUU1UnsGpXivIzpc/ZyFQs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/rsa.h</key>
		<data>
		BXOED7wh3elZhA9q8KoFrXffhUg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/rsaerr.h</key>
		<data>
		jrv7NJXCaI3Ppho9VNnyZFn9jxQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/safestack.h</key>
		<data>
		Cd9WYSUSt9OnXah9XHF3QevCc+E=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/seed.h</key>
		<data>
		xIqmrORS9667meS1VHouBNO1FGI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/sha.h</key>
		<data>
		lu1HA4odImsyOAN6vcDKaHOxMrc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/shim.h</key>
		<data>
		sMxnt7+rU4Gir29aFtcGAwjCdhs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/sm2.h</key>
		<data>
		J9BR3kk+bjp4DUXqRy3dTOnScEM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/sm3.h</key>
		<data>
		H8hFoehBM7TTiupNtJpraPdF6TE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/sm4.h</key>
		<data>
		u11NBly6iFRmqITEgK0HhoQVACA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/srp.h</key>
		<data>
		hs0FaZQgqkGx2ATJtkZLR3E7ajQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/srtp.h</key>
		<data>
		Xiev9lzpqvNNo1eXz+UZl4RZh+0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ssl.h</key>
		<data>
		mhalYjN/qeuEF+l6WIVquvXvjOs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ssl2.h</key>
		<data>
		ZUBF9zzQr/EnSx9hH0JCB0ETP8s=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ssl3.h</key>
		<data>
		cA/eJmiVcOy62x5KksvM5fafytI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/sslerr.h</key>
		<data>
		4EiOgpXMaHQuyZqwDCHkMoCYXhw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/stack.h</key>
		<data>
		HlhyAUEwF7fsEBjOpYfB+ZGw2SU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/store.h</key>
		<data>
		f3f0erlSosQY7bDqrHLGzKe7YY4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/storeerr.h</key>
		<data>
		kv/pt/4V9S0com6azZDBL4S2ef4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/symhacks.h</key>
		<data>
		9immsicIban6k2s+1MF7N3ITwzE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/tls1.h</key>
		<data>
		snV9T38ogNzmGbcPr9Rn32lFHOo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ts.h</key>
		<data>
		iWDV/jVLCzacuMrj1s833l3wAYY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/tserr.h</key>
		<data>
		PI3IsQNlWi/RwHMUbbL1VJDUvj0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/txt_db.h</key>
		<data>
		NoTeslBGWtynVU+GDhyxyzXUdTE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ui.h</key>
		<data>
		++9liaOBXfJY40d+p4saEce2zpw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/uierr.h</key>
		<data>
		R61EyGTilaauZmnHry4jjFiN5ko=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/whrlpool.h</key>
		<data>
		Uo0K/dGVqhsRUor/8CFpmWNaoHY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/x509.h</key>
		<data>
		5wWbXaF8/6/pZR0i8O64cxA2vYY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/x509_vfy.h</key>
		<data>
		+5MyYNetMS2YX3CRFqSzMEZc4TE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/x509err.h</key>
		<data>
		z4/1nUkbgTSuqRKSBA/+tS9zTD4=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/x509v3.h</key>
		<data>
		YhR8NOnXe50FuvmDc73j2oiOZGo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/x509v3err.h</key>
		<data>
		X1T6LneJ+WEeudGjNgNYP08RqMU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		wSMjVnQJnXCQkrl0p+m5ijRHe3c=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/OpenSSL</key>
		<data>
		cnIBb+QkjFdekqhD73pTvwC3zPM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Resources/Info.plist</key>
		<data>
		8HGXjkay6IFtM2LDnzd3SY7GTWo=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<data>
		AL1dh5ctObXBjoBiabSJ86M3HQs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/_CodeSignature/CodeDirectory</key>
		<data>
		YyfuX/muGBjIohq9hzzjy210Vro=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/_CodeSignature/CodeRequirements</key>
		<data>
		LivNFy9yoqb1NZc0y0RjgYMqdYM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/_CodeSignature/CodeRequirements-1</key>
		<data>
		w/iugVcTIRZ9zywyochI66lNZIA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/_CodeSignature/CodeResources</key>
		<data>
		YUVuK7fxmxCwROOppC8PTDTP+5U=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/_CodeSignature/CodeSignature</key>
		<data>
		TKuI2RAsdFMM4MO1dIqopQxg6Jg=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/OpenSSL.h</key>
		<data>
		tx85aV3apVi3nD5Fm04TxZsWR60=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/aes.h</key>
		<data>
		/QHXsfp5KZBtt0hpQ+PGhRB5TQE=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/asn1.h</key>
		<data>
		/JULnimbzTltoXNqIFMZvE15UFw=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/asn1err.h</key>
		<data>
		vClK4nFx8VUb19HBjU1cQuNfemY=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/asn1t.h</key>
		<data>
		LWrSPPRO1SZ6tiCyd9G2baHBT0g=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/async.h</key>
		<data>
		Du5KwAWX1O4Ld/G5CC+CgQkQU9M=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/asyncerr.h</key>
		<data>
		K5Jc+TuPuL/d4mP5tAhHKE2LRmc=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/bio.h</key>
		<data>
		3e8z1vvxgbXPwmeg+1bRSmgfCG8=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/bioerr.h</key>
		<data>
		N0rVDI9qQwyDEWvlqW1vGQrh0RU=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/blowfish.h</key>
		<data>
		BLqJpLWCl4Gl0DR4WO0luoyixMg=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/bn.h</key>
		<data>
		zwKCI0fcYjm7MT9ZsUzGG4BDLTE=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/bnerr.h</key>
		<data>
		ybn9OmsYdCvnSKGlXSQQJ8b+Y44=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/buffer.h</key>
		<data>
		70BHajzy0JGUYwo7F+dpD0CeWrs=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/buffererr.h</key>
		<data>
		PSVdu9joU+POeEegSPtAPctXWyI=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/camellia.h</key>
		<data>
		R0cxfQe4VMejfw/FBnV5jlrTxS8=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/cast.h</key>
		<data>
		tg9fweKyld2MF5c1iw7sEh5btDM=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/cmac.h</key>
		<data>
		SsfJcPvnO3RZ7i+QyWeuyIBoFr4=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/cms.h</key>
		<data>
		WhfyS38eVlM/0EjH+SRNVL1fhB0=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/cmserr.h</key>
		<data>
		icv8DehBtI8fn3XrZvJ8GaK3Nm8=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/comp.h</key>
		<data>
		iXVcuTWmFCf9QLEmp9sNP8JQUw0=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/comperr.h</key>
		<data>
		smXqglFOEgRt0CXetpBcXnWbZYI=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/conf.h</key>
		<data>
		lY422quRPGGoMtvoZu73RVfY38s=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/conf_api.h</key>
		<data>
		aEkI7MCNJGZ+SJxs514uMY1oW3s=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/conferr.h</key>
		<data>
		HhxJvI2jMpKEWKwpMC+m/3YD5tE=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/crypto.h</key>
		<data>
		9mlWjKYcqA63rFf05YOE/OnlLlE=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/cryptoerr.h</key>
		<data>
		rvUn24W+SZM/y6GTW6NG1DW8KQo=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ct.h</key>
		<data>
		cpeLfPNApLUZ414BRmIcahaW0y0=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/cterr.h</key>
		<data>
		z6hnmIDS0tby0u7XWscijJOnipQ=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/des.h</key>
		<data>
		znOw/0Vq2B1QWQ5QlySAEIkrdwE=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/dh.h</key>
		<data>
		9zO15LRHFl5v1MTrCuS3Q2iGGu0=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/dherr.h</key>
		<data>
		PTwLUzp4LhfTwc/VMpRyT9YTuRU=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/dsa.h</key>
		<data>
		AEYyYVu2fy7yPpiyRyjyypHNCKI=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/dsaerr.h</key>
		<data>
		jmOqsG1TSs1yTIm74Qstlz/KODg=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/dtls1.h</key>
		<data>
		fDDC+ixg7o1tDB4basD847J6+Zk=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/e_os2.h</key>
		<data>
		lavqS62GkOuhGihyu+WOjVd92SM=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ebcdic.h</key>
		<data>
		z5Fn9TbPaQo8zoY+Uwo/lSr9SJ8=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ec.h</key>
		<data>
		VDanIs80uH9tNKL+PSMmYL0YIu4=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ecdh.h</key>
		<data>
		iXUqwjlcXCix2i1+T/zXRV4PU18=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ecdsa.h</key>
		<data>
		iXUqwjlcXCix2i1+T/zXRV4PU18=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ecerr.h</key>
		<data>
		GyM9ZofjjCqJIe+IbO9j4p+HDZk=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/engine.h</key>
		<data>
		dS4eXHBYjCoSL3nDXCYuyZR9V9c=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/engineerr.h</key>
		<data>
		H6oGO47GeHqzTYa913WPlF5cZzQ=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/err.h</key>
		<data>
		OZsmLZ7UryVhV7QY17J5cymnWlY=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/evp.h</key>
		<data>
		Ue0l0jetFcNRP+2k4ve5l9kWsOw=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/evperr.h</key>
		<data>
		YPfW2qvVYScvnOl+vKfUlzf4pvg=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/hmac.h</key>
		<data>
		oNXOm72CsIiR3i2/7dzmPfXHpnk=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/idea.h</key>
		<data>
		9OhfGjNERiWm+IaFZng3mj74a70=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/kdf.h</key>
		<data>
		8bAA2Z5x72kQuKB9lY8B07YpOvA=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/kdferr.h</key>
		<data>
		eBl7XkjAByV3fh1j+2ZSsSVdPd0=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/lhash.h</key>
		<data>
		gsp3p9sYfgyLm8w777aVi7LZzaA=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/md2.h</key>
		<data>
		SU5g+hFH8KXJwSElUE6qnz88XbQ=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/md4.h</key>
		<data>
		NVmYVdXaFSHylpRJRh52LUqSAIY=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/md5.h</key>
		<data>
		8R2dids4HGec0BuJ5RjnI0sNAqs=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/mdc2.h</key>
		<data>
		ceP5kO5gOJDJGS7HrDRjpWWG2i4=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/modes.h</key>
		<data>
		oykGDYKdMkpM+dh+C5148xyIG9A=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/obj_mac.h</key>
		<data>
		0xczM7tqfkjs0KVU5TgD3MmB0O8=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/objects.h</key>
		<data>
		1BKkzZQ86VAQYSD2wS3W+kAe8lU=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/objectserr.h</key>
		<data>
		62CLZz09xeqGq4nUpt1HfuvRrbM=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ocsp.h</key>
		<data>
		/vPykCbZnxZ/oD4x73y3vCFlfAg=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ocsperr.h</key>
		<data>
		HzVTI0XxPNFGeTAYcha32Bf1qLw=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/opensslconf.h</key>
		<data>
		J4AlQEAw2IES9GWsNFdc2SJQhl4=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/opensslv.h</key>
		<data>
		RV0inZj+UfBzOwFBUF97hvsSAjg=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ossl_typ.h</key>
		<data>
		/hD+IfNYoPQpFE2wIl8jHi6BRVI=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/pem.h</key>
		<data>
		32ly0pNLPzdTYGECzCbGY6huGuw=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/pem2.h</key>
		<data>
		t2jj9ipWiYEJZvgm7/w68FCv7xk=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/pemerr.h</key>
		<data>
		LPK/t/8oy0h9S1TUlxQnvG640ps=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/pkcs12.h</key>
		<data>
		Wgtt5Ajf2I0Qe0Yw/C6/da673Vs=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/pkcs12err.h</key>
		<data>
		iYGY/k8EiROz+H6/d3nIzlOnXHY=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/pkcs7.h</key>
		<data>
		DSVKNOqJWVpNEj9g1afFP/AAHbs=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/pkcs7err.h</key>
		<data>
		vbVkdgXZDNlmUBjHsDcvjoyWNTE=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/rand.h</key>
		<data>
		BUI/kpuBobNUPlXrW/OkHeq9ClM=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/rand_drbg.h</key>
		<data>
		dZJZsnraib5Oxt12qSJ4bXxP3/w=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/randerr.h</key>
		<data>
		/b3o6sjMLxkewJmLW7LeYF/10A0=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/rc2.h</key>
		<data>
		Xzwvx1iv4W35klxWCpyRR35/Uwc=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/rc4.h</key>
		<data>
		01mH39v8pvXIdzB3N/3bn0uJwVs=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/rc5.h</key>
		<data>
		nVMdNFdbOheiSzNQjJ5v92LvEmI=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ripemd.h</key>
		<data>
		UUNVXGUU1UnsGpXivIzpc/ZyFQs=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/rsa.h</key>
		<data>
		BXOED7wh3elZhA9q8KoFrXffhUg=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/rsaerr.h</key>
		<data>
		jrv7NJXCaI3Ppho9VNnyZFn9jxQ=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/safestack.h</key>
		<data>
		Cd9WYSUSt9OnXah9XHF3QevCc+E=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/seed.h</key>
		<data>
		xIqmrORS9667meS1VHouBNO1FGI=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/sha.h</key>
		<data>
		lu1HA4odImsyOAN6vcDKaHOxMrc=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/shim.h</key>
		<data>
		sMxnt7+rU4Gir29aFtcGAwjCdhs=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/sm2.h</key>
		<data>
		J9BR3kk+bjp4DUXqRy3dTOnScEM=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/sm3.h</key>
		<data>
		H8hFoehBM7TTiupNtJpraPdF6TE=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/sm4.h</key>
		<data>
		u11NBly6iFRmqITEgK0HhoQVACA=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/srp.h</key>
		<data>
		hs0FaZQgqkGx2ATJtkZLR3E7ajQ=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/srtp.h</key>
		<data>
		Xiev9lzpqvNNo1eXz+UZl4RZh+0=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ssl.h</key>
		<data>
		mhalYjN/qeuEF+l6WIVquvXvjOs=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ssl2.h</key>
		<data>
		ZUBF9zzQr/EnSx9hH0JCB0ETP8s=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ssl3.h</key>
		<data>
		cA/eJmiVcOy62x5KksvM5fafytI=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/sslerr.h</key>
		<data>
		4EiOgpXMaHQuyZqwDCHkMoCYXhw=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/stack.h</key>
		<data>
		HlhyAUEwF7fsEBjOpYfB+ZGw2SU=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/store.h</key>
		<data>
		f3f0erlSosQY7bDqrHLGzKe7YY4=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/storeerr.h</key>
		<data>
		kv/pt/4V9S0com6azZDBL4S2ef4=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/symhacks.h</key>
		<data>
		9immsicIban6k2s+1MF7N3ITwzE=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/tls1.h</key>
		<data>
		snV9T38ogNzmGbcPr9Rn32lFHOo=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ts.h</key>
		<data>
		iWDV/jVLCzacuMrj1s833l3wAYY=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/tserr.h</key>
		<data>
		PI3IsQNlWi/RwHMUbbL1VJDUvj0=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/txt_db.h</key>
		<data>
		NoTeslBGWtynVU+GDhyxyzXUdTE=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ui.h</key>
		<data>
		++9liaOBXfJY40d+p4saEce2zpw=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/uierr.h</key>
		<data>
		R61EyGTilaauZmnHry4jjFiN5ko=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/whrlpool.h</key>
		<data>
		Uo0K/dGVqhsRUor/8CFpmWNaoHY=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/x509.h</key>
		<data>
		5wWbXaF8/6/pZR0i8O64cxA2vYY=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/x509_vfy.h</key>
		<data>
		+5MyYNetMS2YX3CRFqSzMEZc4TE=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/x509err.h</key>
		<data>
		z4/1nUkbgTSuqRKSBA/+tS9zTD4=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/x509v3.h</key>
		<data>
		YhR8NOnXe50FuvmDc73j2oiOZGo=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/x509v3err.h</key>
		<data>
		X1T6LneJ+WEeudGjNgNYP08RqMU=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Info.plist</key>
		<data>
		or0PHfwEUfPWO1329QfRYfrjYp8=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Modules/module.modulemap</key>
		<data>
		wSMjVnQJnXCQkrl0p+m5ijRHe3c=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/OpenSSL</key>
		<data>
		aVgJxBiHccZVdkegDSyK0AIjciU=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/PrivacyInfo.xcprivacy</key>
		<data>
		AL1dh5ctObXBjoBiabSJ86M3HQs=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/_CodeSignature/CodeDirectory</key>
		<data>
		nZrgLKdcnfl/v6/JXfo3UKn0X8A=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/_CodeSignature/CodeRequirements-1</key>
		<data>
		J4qTysq8UHbs7drizZAHzRAr2uo=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/_CodeSignature/CodeResources</key>
		<data>
		syqOt6hQ6uT0w4z/YhfxE1+8OK0=
		</data>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/OpenSSL.h</key>
		<data>
		dH3RGwhv9Otw3o0azCaKlGRCtgQ=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/aes.h</key>
		<data>
		/QHXsfp5KZBtt0hpQ+PGhRB5TQE=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/asn1.h</key>
		<data>
		/JULnimbzTltoXNqIFMZvE15UFw=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/asn1err.h</key>
		<data>
		vClK4nFx8VUb19HBjU1cQuNfemY=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/asn1t.h</key>
		<data>
		LWrSPPRO1SZ6tiCyd9G2baHBT0g=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/async.h</key>
		<data>
		Du5KwAWX1O4Ld/G5CC+CgQkQU9M=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/asyncerr.h</key>
		<data>
		K5Jc+TuPuL/d4mP5tAhHKE2LRmc=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/bio.h</key>
		<data>
		3e8z1vvxgbXPwmeg+1bRSmgfCG8=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/bioerr.h</key>
		<data>
		N0rVDI9qQwyDEWvlqW1vGQrh0RU=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/blowfish.h</key>
		<data>
		BLqJpLWCl4Gl0DR4WO0luoyixMg=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/bn.h</key>
		<data>
		zwKCI0fcYjm7MT9ZsUzGG4BDLTE=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/bnerr.h</key>
		<data>
		ybn9OmsYdCvnSKGlXSQQJ8b+Y44=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/buffer.h</key>
		<data>
		70BHajzy0JGUYwo7F+dpD0CeWrs=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/buffererr.h</key>
		<data>
		PSVdu9joU+POeEegSPtAPctXWyI=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/camellia.h</key>
		<data>
		R0cxfQe4VMejfw/FBnV5jlrTxS8=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/cast.h</key>
		<data>
		tg9fweKyld2MF5c1iw7sEh5btDM=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/cmac.h</key>
		<data>
		SsfJcPvnO3RZ7i+QyWeuyIBoFr4=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/cms.h</key>
		<data>
		WhfyS38eVlM/0EjH+SRNVL1fhB0=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/cmserr.h</key>
		<data>
		icv8DehBtI8fn3XrZvJ8GaK3Nm8=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/comp.h</key>
		<data>
		iXVcuTWmFCf9QLEmp9sNP8JQUw0=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/comperr.h</key>
		<data>
		smXqglFOEgRt0CXetpBcXnWbZYI=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/conf.h</key>
		<data>
		lY422quRPGGoMtvoZu73RVfY38s=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/conf_api.h</key>
		<data>
		aEkI7MCNJGZ+SJxs514uMY1oW3s=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/conferr.h</key>
		<data>
		HhxJvI2jMpKEWKwpMC+m/3YD5tE=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/crypto.h</key>
		<data>
		9mlWjKYcqA63rFf05YOE/OnlLlE=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/cryptoerr.h</key>
		<data>
		rvUn24W+SZM/y6GTW6NG1DW8KQo=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ct.h</key>
		<data>
		cpeLfPNApLUZ414BRmIcahaW0y0=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/cterr.h</key>
		<data>
		z6hnmIDS0tby0u7XWscijJOnipQ=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/des.h</key>
		<data>
		znOw/0Vq2B1QWQ5QlySAEIkrdwE=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/dh.h</key>
		<data>
		9zO15LRHFl5v1MTrCuS3Q2iGGu0=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/dherr.h</key>
		<data>
		PTwLUzp4LhfTwc/VMpRyT9YTuRU=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/dsa.h</key>
		<data>
		AEYyYVu2fy7yPpiyRyjyypHNCKI=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/dsaerr.h</key>
		<data>
		jmOqsG1TSs1yTIm74Qstlz/KODg=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/dtls1.h</key>
		<data>
		fDDC+ixg7o1tDB4basD847J6+Zk=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/e_os2.h</key>
		<data>
		lavqS62GkOuhGihyu+WOjVd92SM=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ebcdic.h</key>
		<data>
		z5Fn9TbPaQo8zoY+Uwo/lSr9SJ8=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ec.h</key>
		<data>
		VDanIs80uH9tNKL+PSMmYL0YIu4=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ecdh.h</key>
		<data>
		iXUqwjlcXCix2i1+T/zXRV4PU18=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ecdsa.h</key>
		<data>
		iXUqwjlcXCix2i1+T/zXRV4PU18=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ecerr.h</key>
		<data>
		GyM9ZofjjCqJIe+IbO9j4p+HDZk=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/engine.h</key>
		<data>
		dS4eXHBYjCoSL3nDXCYuyZR9V9c=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/engineerr.h</key>
		<data>
		H6oGO47GeHqzTYa913WPlF5cZzQ=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/err.h</key>
		<data>
		OZsmLZ7UryVhV7QY17J5cymnWlY=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/evp.h</key>
		<data>
		Ue0l0jetFcNRP+2k4ve5l9kWsOw=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/evperr.h</key>
		<data>
		YPfW2qvVYScvnOl+vKfUlzf4pvg=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/hmac.h</key>
		<data>
		oNXOm72CsIiR3i2/7dzmPfXHpnk=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/idea.h</key>
		<data>
		9OhfGjNERiWm+IaFZng3mj74a70=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/kdf.h</key>
		<data>
		8bAA2Z5x72kQuKB9lY8B07YpOvA=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/kdferr.h</key>
		<data>
		eBl7XkjAByV3fh1j+2ZSsSVdPd0=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/lhash.h</key>
		<data>
		gsp3p9sYfgyLm8w777aVi7LZzaA=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/md2.h</key>
		<data>
		SU5g+hFH8KXJwSElUE6qnz88XbQ=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/md4.h</key>
		<data>
		NVmYVdXaFSHylpRJRh52LUqSAIY=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/md5.h</key>
		<data>
		8R2dids4HGec0BuJ5RjnI0sNAqs=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/mdc2.h</key>
		<data>
		ceP5kO5gOJDJGS7HrDRjpWWG2i4=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/modes.h</key>
		<data>
		oykGDYKdMkpM+dh+C5148xyIG9A=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/obj_mac.h</key>
		<data>
		0xczM7tqfkjs0KVU5TgD3MmB0O8=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/objects.h</key>
		<data>
		1BKkzZQ86VAQYSD2wS3W+kAe8lU=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/objectserr.h</key>
		<data>
		62CLZz09xeqGq4nUpt1HfuvRrbM=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ocsp.h</key>
		<data>
		/vPykCbZnxZ/oD4x73y3vCFlfAg=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ocsperr.h</key>
		<data>
		HzVTI0XxPNFGeTAYcha32Bf1qLw=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/opensslconf.h</key>
		<data>
		yNGszHsg1rO96Mwyt+XRNUDMH3E=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/opensslv.h</key>
		<data>
		RV0inZj+UfBzOwFBUF97hvsSAjg=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ossl_typ.h</key>
		<data>
		/hD+IfNYoPQpFE2wIl8jHi6BRVI=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/pem.h</key>
		<data>
		32ly0pNLPzdTYGECzCbGY6huGuw=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/pem2.h</key>
		<data>
		t2jj9ipWiYEJZvgm7/w68FCv7xk=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/pemerr.h</key>
		<data>
		LPK/t/8oy0h9S1TUlxQnvG640ps=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/pkcs12.h</key>
		<data>
		Wgtt5Ajf2I0Qe0Yw/C6/da673Vs=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/pkcs12err.h</key>
		<data>
		iYGY/k8EiROz+H6/d3nIzlOnXHY=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/pkcs7.h</key>
		<data>
		DSVKNOqJWVpNEj9g1afFP/AAHbs=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/pkcs7err.h</key>
		<data>
		vbVkdgXZDNlmUBjHsDcvjoyWNTE=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/rand.h</key>
		<data>
		BUI/kpuBobNUPlXrW/OkHeq9ClM=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/rand_drbg.h</key>
		<data>
		dZJZsnraib5Oxt12qSJ4bXxP3/w=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/randerr.h</key>
		<data>
		/b3o6sjMLxkewJmLW7LeYF/10A0=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/rc2.h</key>
		<data>
		Xzwvx1iv4W35klxWCpyRR35/Uwc=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/rc4.h</key>
		<data>
		01mH39v8pvXIdzB3N/3bn0uJwVs=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/rc5.h</key>
		<data>
		nVMdNFdbOheiSzNQjJ5v92LvEmI=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ripemd.h</key>
		<data>
		UUNVXGUU1UnsGpXivIzpc/ZyFQs=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/rsa.h</key>
		<data>
		BXOED7wh3elZhA9q8KoFrXffhUg=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/rsaerr.h</key>
		<data>
		jrv7NJXCaI3Ppho9VNnyZFn9jxQ=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/safestack.h</key>
		<data>
		Cd9WYSUSt9OnXah9XHF3QevCc+E=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/seed.h</key>
		<data>
		xIqmrORS9667meS1VHouBNO1FGI=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/sha.h</key>
		<data>
		lu1HA4odImsyOAN6vcDKaHOxMrc=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/shim.h</key>
		<data>
		sMxnt7+rU4Gir29aFtcGAwjCdhs=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/sm2.h</key>
		<data>
		J9BR3kk+bjp4DUXqRy3dTOnScEM=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/sm3.h</key>
		<data>
		H8hFoehBM7TTiupNtJpraPdF6TE=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/sm4.h</key>
		<data>
		u11NBly6iFRmqITEgK0HhoQVACA=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/srp.h</key>
		<data>
		hs0FaZQgqkGx2ATJtkZLR3E7ajQ=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/srtp.h</key>
		<data>
		Xiev9lzpqvNNo1eXz+UZl4RZh+0=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ssl.h</key>
		<data>
		mhalYjN/qeuEF+l6WIVquvXvjOs=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ssl2.h</key>
		<data>
		ZUBF9zzQr/EnSx9hH0JCB0ETP8s=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ssl3.h</key>
		<data>
		cA/eJmiVcOy62x5KksvM5fafytI=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/sslerr.h</key>
		<data>
		4EiOgpXMaHQuyZqwDCHkMoCYXhw=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/stack.h</key>
		<data>
		HlhyAUEwF7fsEBjOpYfB+ZGw2SU=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/store.h</key>
		<data>
		f3f0erlSosQY7bDqrHLGzKe7YY4=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/storeerr.h</key>
		<data>
		kv/pt/4V9S0com6azZDBL4S2ef4=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/symhacks.h</key>
		<data>
		9immsicIban6k2s+1MF7N3ITwzE=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/tls1.h</key>
		<data>
		snV9T38ogNzmGbcPr9Rn32lFHOo=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ts.h</key>
		<data>
		iWDV/jVLCzacuMrj1s833l3wAYY=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/tserr.h</key>
		<data>
		PI3IsQNlWi/RwHMUbbL1VJDUvj0=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/txt_db.h</key>
		<data>
		NoTeslBGWtynVU+GDhyxyzXUdTE=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ui.h</key>
		<data>
		++9liaOBXfJY40d+p4saEce2zpw=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/uierr.h</key>
		<data>
		R61EyGTilaauZmnHry4jjFiN5ko=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/whrlpool.h</key>
		<data>
		Uo0K/dGVqhsRUor/8CFpmWNaoHY=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/x509.h</key>
		<data>
		5wWbXaF8/6/pZR0i8O64cxA2vYY=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/x509_vfy.h</key>
		<data>
		+5MyYNetMS2YX3CRFqSzMEZc4TE=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/x509err.h</key>
		<data>
		z4/1nUkbgTSuqRKSBA/+tS9zTD4=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/x509v3.h</key>
		<data>
		YhR8NOnXe50FuvmDc73j2oiOZGo=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/x509v3err.h</key>
		<data>
		X1T6LneJ+WEeudGjNgNYP08RqMU=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		wSMjVnQJnXCQkrl0p+m5ijRHe3c=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/OpenSSL</key>
		<data>
		LqY/lhq9wJw+eouFUuS8/mp8QKo=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Resources/Info.plist</key>
		<data>
		gyD3g6vYmIQTZWlTzAiL16f0sAs=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<data>
		AL1dh5ctObXBjoBiabSJ86M3HQs=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/_CodeSignature/CodeDirectory</key>
		<data>
		WYWAuL5uXgb1hrGtV5jcrQ6Wbpc=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/_CodeSignature/CodeRequirements</key>
		<data>
		LivNFy9yoqb1NZc0y0RjgYMqdYM=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/_CodeSignature/CodeRequirements-1</key>
		<data>
		FVWme/rGk9UIXXP7Xj+3u/yI1q8=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/_CodeSignature/CodeResources</key>
		<data>
		B9AkY04jmtO5OEKNiJvtpb8FdHE=
		</data>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/_CodeSignature/CodeSignature</key>
		<data>
		jfkdt/uE2KQtM0Ug6YyjoPNtNII=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/OpenSSL.framework/Headers/OpenSSL.h</key>
		<dict>
			<key>hash</key>
			<data>
			TYMr1SKbMS+Pm94I583X46xmkDc=
			</data>
			<key>hash2</key>
			<data>
			cS7vM4LFBnKS8rmqpJq/kU1RjeE45QFwlMG1g2yq6dQ=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/aes.h</key>
		<dict>
			<key>hash</key>
			<data>
			/QHXsfp5KZBtt0hpQ+PGhRB5TQE=
			</data>
			<key>hash2</key>
			<data>
			9ron6H2X6lePAVQKj4S36rBdQsF4utznEtDMZo2KmYE=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/asn1.h</key>
		<dict>
			<key>hash</key>
			<data>
			/JULnimbzTltoXNqIFMZvE15UFw=
			</data>
			<key>hash2</key>
			<data>
			hRMTsRdrqj1olqru6+9WIW69043Nq7ObPso2tmRTUrw=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/asn1err.h</key>
		<dict>
			<key>hash</key>
			<data>
			vClK4nFx8VUb19HBjU1cQuNfemY=
			</data>
			<key>hash2</key>
			<data>
			uJsWIW3dNfAo9b/T/g208urWyUFed0m3EVsDk7Qn7y8=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/asn1t.h</key>
		<dict>
			<key>hash</key>
			<data>
			LWrSPPRO1SZ6tiCyd9G2baHBT0g=
			</data>
			<key>hash2</key>
			<data>
			JwL1aTAiSLgijkkxd88KRp8Sfxzlx2vGhLyQ+9/EV0M=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/async.h</key>
		<dict>
			<key>hash</key>
			<data>
			Du5KwAWX1O4Ld/G5CC+CgQkQU9M=
			</data>
			<key>hash2</key>
			<data>
			75bHFsrDk/LNbaMErwKRVezZ8CGGnNWkw0Hr3ySQNrw=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/asyncerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			K5Jc+TuPuL/d4mP5tAhHKE2LRmc=
			</data>
			<key>hash2</key>
			<data>
			ysycqVGqUj0+1rnfI2YCJjOSWmcp0wzRNKBdL/5fsVY=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/bio.h</key>
		<dict>
			<key>hash</key>
			<data>
			3e8z1vvxgbXPwmeg+1bRSmgfCG8=
			</data>
			<key>hash2</key>
			<data>
			eLBVVVlX7sK/tM5fkbJ73P33MSgCa+nOhfzKASb9zT0=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/bioerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			N0rVDI9qQwyDEWvlqW1vGQrh0RU=
			</data>
			<key>hash2</key>
			<data>
			rSn+TvN/18SyVraQyvk3GnTimYH2VhuUQ22sVKYb90s=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/blowfish.h</key>
		<dict>
			<key>hash</key>
			<data>
			BLqJpLWCl4Gl0DR4WO0luoyixMg=
			</data>
			<key>hash2</key>
			<data>
			gTssfP+Ii1twm5DU2lhK/vWbXOHqiIYX1oLxr0hXj68=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/bn.h</key>
		<dict>
			<key>hash</key>
			<data>
			zwKCI0fcYjm7MT9ZsUzGG4BDLTE=
			</data>
			<key>hash2</key>
			<data>
			j3mynFrUeRhqyq6WKlmNmGjcQ0/V+kmgCO7Pr/G5mMo=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/bnerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			ybn9OmsYdCvnSKGlXSQQJ8b+Y44=
			</data>
			<key>hash2</key>
			<data>
			gJirlUUwfW4snMKK6ZXW/aDolZthZ8wD1ncWsCq7J/8=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/buffer.h</key>
		<dict>
			<key>hash</key>
			<data>
			70BHajzy0JGUYwo7F+dpD0CeWrs=
			</data>
			<key>hash2</key>
			<data>
			pPopK5A8uMLOF2G6SYDPW9+2bcrcu1yK7MRbASvGTCM=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/buffererr.h</key>
		<dict>
			<key>hash</key>
			<data>
			PSVdu9joU+POeEegSPtAPctXWyI=
			</data>
			<key>hash2</key>
			<data>
			ci9uqHcRt8DLbuKcFXYsKDnlrRe571ebboG2Eh8DXDA=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/camellia.h</key>
		<dict>
			<key>hash</key>
			<data>
			R0cxfQe4VMejfw/FBnV5jlrTxS8=
			</data>
			<key>hash2</key>
			<data>
			VhvbLphUWOgJ5lbGDkvIPh1nB3Rt2LW628ELZBmNR3A=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/cast.h</key>
		<dict>
			<key>hash</key>
			<data>
			tg9fweKyld2MF5c1iw7sEh5btDM=
			</data>
			<key>hash2</key>
			<data>
			oUxRoSmzrleV2v2YBlvguwy/ALTUUo6Frb9imWb0L1M=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/cmac.h</key>
		<dict>
			<key>hash</key>
			<data>
			SsfJcPvnO3RZ7i+QyWeuyIBoFr4=
			</data>
			<key>hash2</key>
			<data>
			db+V0doRj/Hi8UPMt6Vk2UneRACXuXpJclwDl24We1I=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/cms.h</key>
		<dict>
			<key>hash</key>
			<data>
			WhfyS38eVlM/0EjH+SRNVL1fhB0=
			</data>
			<key>hash2</key>
			<data>
			B+Swp3nJV++QJtacDLWmId3CBgr5Z+DdXpGshh/fvbI=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/cmserr.h</key>
		<dict>
			<key>hash</key>
			<data>
			icv8DehBtI8fn3XrZvJ8GaK3Nm8=
			</data>
			<key>hash2</key>
			<data>
			fg/dq6hfs9ESYq7ixgJyWS3uGmI0pSqUsGuv0cClQ1A=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/comp.h</key>
		<dict>
			<key>hash</key>
			<data>
			iXVcuTWmFCf9QLEmp9sNP8JQUw0=
			</data>
			<key>hash2</key>
			<data>
			mKh3xicj5gQuTkdAzRpud5nfdS2SNDhtc6KKCY5usxw=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/comperr.h</key>
		<dict>
			<key>hash</key>
			<data>
			smXqglFOEgRt0CXetpBcXnWbZYI=
			</data>
			<key>hash2</key>
			<data>
			9kviBdCK8lVxh+wZwDZ4qp4p9m5CjeKc691izXw7VRU=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/conf.h</key>
		<dict>
			<key>hash</key>
			<data>
			lY422quRPGGoMtvoZu73RVfY38s=
			</data>
			<key>hash2</key>
			<data>
			5UNB4wUj2twJ2CE2b1r7E8kobVQLNMGUVAaRjtWI+iE=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/conf_api.h</key>
		<dict>
			<key>hash</key>
			<data>
			aEkI7MCNJGZ+SJxs514uMY1oW3s=
			</data>
			<key>hash2</key>
			<data>
			F1+GtptYsM0dpsmyfYerTk7VLzfcdreJXCEMQ0aVBOs=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/conferr.h</key>
		<dict>
			<key>hash</key>
			<data>
			HhxJvI2jMpKEWKwpMC+m/3YD5tE=
			</data>
			<key>hash2</key>
			<data>
			q/4mSFzUOoDgxHbmKJeWEq0opjVXe69o7uR2hQQlpJg=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/crypto.h</key>
		<dict>
			<key>hash</key>
			<data>
			9mlWjKYcqA63rFf05YOE/OnlLlE=
			</data>
			<key>hash2</key>
			<data>
			0s2E5pXmgFH+Z/p8+QHMuk+tzMcaazAJDfIKAbkdPsc=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/cryptoerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			rvUn24W+SZM/y6GTW6NG1DW8KQo=
			</data>
			<key>hash2</key>
			<data>
			51LX09oypsAJzyZEUHJjZ/1p58KkwYXVgM5lACHWjno=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/ct.h</key>
		<dict>
			<key>hash</key>
			<data>
			cpeLfPNApLUZ414BRmIcahaW0y0=
			</data>
			<key>hash2</key>
			<data>
			snqvDTlglQC0/OOztl9nUhFqyzBCDeHVOUMHSkgQQ/A=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/cterr.h</key>
		<dict>
			<key>hash</key>
			<data>
			z6hnmIDS0tby0u7XWscijJOnipQ=
			</data>
			<key>hash2</key>
			<data>
			q25wtS40EkfEsheIOYD/HpjkRT0QILCwOM2/CWxKuKU=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/des.h</key>
		<dict>
			<key>hash</key>
			<data>
			znOw/0Vq2B1QWQ5QlySAEIkrdwE=
			</data>
			<key>hash2</key>
			<data>
			PLaPu0mONOGeRvnUFWFZaracz3ix2L3y8xLoksBgAV8=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/dh.h</key>
		<dict>
			<key>hash</key>
			<data>
			9zO15LRHFl5v1MTrCuS3Q2iGGu0=
			</data>
			<key>hash2</key>
			<data>
			RtDEiea4BjeSz52Bk/UnOPDY/spOuERV4KNZY21BqeA=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/dherr.h</key>
		<dict>
			<key>hash</key>
			<data>
			PTwLUzp4LhfTwc/VMpRyT9YTuRU=
			</data>
			<key>hash2</key>
			<data>
			cpkNxez4V74yHIi5IVAshTZX+AnsU+r5GgEcCZqFdw0=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/dsa.h</key>
		<dict>
			<key>hash</key>
			<data>
			AEYyYVu2fy7yPpiyRyjyypHNCKI=
			</data>
			<key>hash2</key>
			<data>
			FSXlrfJCZDp9NjElgDFPXOYeyt+CIjpJ/1wyIg14+bk=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/dsaerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			jmOqsG1TSs1yTIm74Qstlz/KODg=
			</data>
			<key>hash2</key>
			<data>
			ZeqLR8slroLWETN81H2nwcXNuUUZgr7snYQUE9SpI6k=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/dtls1.h</key>
		<dict>
			<key>hash</key>
			<data>
			fDDC+ixg7o1tDB4basD847J6+Zk=
			</data>
			<key>hash2</key>
			<data>
			cwTxfMmZi9fBZTmuml6lRfLgxkCG1eaIyroQS2p//sw=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/e_os2.h</key>
		<dict>
			<key>hash</key>
			<data>
			lavqS62GkOuhGihyu+WOjVd92SM=
			</data>
			<key>hash2</key>
			<data>
			DWGWKc2z+15UMR6KP/YC+DjEpFMKVr4/0KgefosQy4Q=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/ebcdic.h</key>
		<dict>
			<key>hash</key>
			<data>
			z5Fn9TbPaQo8zoY+Uwo/lSr9SJ8=
			</data>
			<key>hash2</key>
			<data>
			IonxTxHnWsc50RI2UdFuhB88dONtqkg8H+2fjFwURyA=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/ec.h</key>
		<dict>
			<key>hash</key>
			<data>
			VDanIs80uH9tNKL+PSMmYL0YIu4=
			</data>
			<key>hash2</key>
			<data>
			TmJAZFMZhlYHifhQnf0QvF7YLrZVmeA+07/tV75t8Zk=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/ecdh.h</key>
		<dict>
			<key>hash</key>
			<data>
			iXUqwjlcXCix2i1+T/zXRV4PU18=
			</data>
			<key>hash2</key>
			<data>
			lfuJrdO9MrbUPc8aUdGDm5Fed01xOK+vYY5pDvv0FLs=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/ecdsa.h</key>
		<dict>
			<key>hash</key>
			<data>
			iXUqwjlcXCix2i1+T/zXRV4PU18=
			</data>
			<key>hash2</key>
			<data>
			lfuJrdO9MrbUPc8aUdGDm5Fed01xOK+vYY5pDvv0FLs=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/ecerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			GyM9ZofjjCqJIe+IbO9j4p+HDZk=
			</data>
			<key>hash2</key>
			<data>
			haO2TAZEQgoYKVyAAaBrrV18PoTU+HHJJaG0X7ie8YI=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/engine.h</key>
		<dict>
			<key>hash</key>
			<data>
			dS4eXHBYjCoSL3nDXCYuyZR9V9c=
			</data>
			<key>hash2</key>
			<data>
			qY3weurFeSk7GG6mHDXtzj7r3h/QPYVTVj8YAj3kfJU=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/engineerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			H6oGO47GeHqzTYa913WPlF5cZzQ=
			</data>
			<key>hash2</key>
			<data>
			bz0ekbobeXGSlTDLisPvk0wLgqjNRZyvIk4kGj5a1uk=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/err.h</key>
		<dict>
			<key>hash</key>
			<data>
			OZsmLZ7UryVhV7QY17J5cymnWlY=
			</data>
			<key>hash2</key>
			<data>
			FEcmPwqEDoE1xYbhbD2FjO6Tne3fT9kF45GGmAm02qI=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/evp.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ue0l0jetFcNRP+2k4ve5l9kWsOw=
			</data>
			<key>hash2</key>
			<data>
			nZY73agYApiBcGY8z870N66mkEL0Ku86EatKIXuACu8=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/evperr.h</key>
		<dict>
			<key>hash</key>
			<data>
			YPfW2qvVYScvnOl+vKfUlzf4pvg=
			</data>
			<key>hash2</key>
			<data>
			RdaLRi8lI5SIwCC+5fv2lwCwzXtNwrO6cBW93EmUdqQ=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/hmac.h</key>
		<dict>
			<key>hash</key>
			<data>
			oNXOm72CsIiR3i2/7dzmPfXHpnk=
			</data>
			<key>hash2</key>
			<data>
			djhsInPxBfVMV0ny/IVFc8NxqxhfkkjOKVp0jhJsqug=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/idea.h</key>
		<dict>
			<key>hash</key>
			<data>
			9OhfGjNERiWm+IaFZng3mj74a70=
			</data>
			<key>hash2</key>
			<data>
			uraCxcwGAnyC0SbtjmXX3Z3MdfpGTMvJXX8Wi9accy0=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/kdf.h</key>
		<dict>
			<key>hash</key>
			<data>
			8bAA2Z5x72kQuKB9lY8B07YpOvA=
			</data>
			<key>hash2</key>
			<data>
			qq/UqWK6m58qJGIHbH97SZHZ2H5773HYfpMIEl4OKcA=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/kdferr.h</key>
		<dict>
			<key>hash</key>
			<data>
			eBl7XkjAByV3fh1j+2ZSsSVdPd0=
			</data>
			<key>hash2</key>
			<data>
			5AbLyKinlWvLDgapSUVLSNJwIznK9KLmIawM3pwkY+M=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/lhash.h</key>
		<dict>
			<key>hash</key>
			<data>
			gsp3p9sYfgyLm8w777aVi7LZzaA=
			</data>
			<key>hash2</key>
			<data>
			EDbiCroA4FhblrkaAK53ktwSUBFg4RfR+CSDPef+N1I=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/md2.h</key>
		<dict>
			<key>hash</key>
			<data>
			SU5g+hFH8KXJwSElUE6qnz88XbQ=
			</data>
			<key>hash2</key>
			<data>
			nijwqNXiKXZJrymPXjIJ0y/pSG7bF5PMaYp1fJEnBmI=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/md4.h</key>
		<dict>
			<key>hash</key>
			<data>
			NVmYVdXaFSHylpRJRh52LUqSAIY=
			</data>
			<key>hash2</key>
			<data>
			A9P8nc7sbBaNIZ659ATKCLR41VIWIoNLwkt9RZRXAuQ=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/md5.h</key>
		<dict>
			<key>hash</key>
			<data>
			8R2dids4HGec0BuJ5RjnI0sNAqs=
			</data>
			<key>hash2</key>
			<data>
			oGEqj35p47wWb8GGykS+HlsdAgtdqLW+Ah9z1IwnC4I=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/mdc2.h</key>
		<dict>
			<key>hash</key>
			<data>
			ceP5kO5gOJDJGS7HrDRjpWWG2i4=
			</data>
			<key>hash2</key>
			<data>
			8TsB7AnEXCY0Zz0bmxp5rb1uwyvf+UKHMI4rsnQI5Tc=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/modes.h</key>
		<dict>
			<key>hash</key>
			<data>
			oykGDYKdMkpM+dh+C5148xyIG9A=
			</data>
			<key>hash2</key>
			<data>
			9OUnmH4paib8HAb0+Ja6pfRXt+Z/lJV9F27tRpsP5gI=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/obj_mac.h</key>
		<dict>
			<key>hash</key>
			<data>
			0xczM7tqfkjs0KVU5TgD3MmB0O8=
			</data>
			<key>hash2</key>
			<data>
			gevKIMw3DM9y8xA9EjHGpC4GRzC7IiYal9ht3Q57wIo=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/objects.h</key>
		<dict>
			<key>hash</key>
			<data>
			1BKkzZQ86VAQYSD2wS3W+kAe8lU=
			</data>
			<key>hash2</key>
			<data>
			QBjXxU5vGcMjCvBj9NvIUj8UUFKKSvFal6Ch/z+zujg=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/objectserr.h</key>
		<dict>
			<key>hash</key>
			<data>
			62CLZz09xeqGq4nUpt1HfuvRrbM=
			</data>
			<key>hash2</key>
			<data>
			DsczDBIq4rMXTfld3s6mX/ZhphUs0BklKd0UEe8/YqE=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/ocsp.h</key>
		<dict>
			<key>hash</key>
			<data>
			/vPykCbZnxZ/oD4x73y3vCFlfAg=
			</data>
			<key>hash2</key>
			<data>
			z7vDQ0tWxGcQgDJq64w9AH2rfSw2IdUUbBGro0cVi4A=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/ocsperr.h</key>
		<dict>
			<key>hash</key>
			<data>
			HzVTI0XxPNFGeTAYcha32Bf1qLw=
			</data>
			<key>hash2</key>
			<data>
			xzpXsZGWAf+MBPLcnGLb0TDvLTrjm7D83yW8nm6vMno=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/opensslconf.h</key>
		<dict>
			<key>hash</key>
			<data>
			JcwLt3wVdxf0h0NZ9jm1E8n98Z8=
			</data>
			<key>hash2</key>
			<data>
			SuWGKFAcCcjq3MrR54kmRkk2XgrcfMa2hGjjdgTpg0s=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/opensslv.h</key>
		<dict>
			<key>hash</key>
			<data>
			RV0inZj+UfBzOwFBUF97hvsSAjg=
			</data>
			<key>hash2</key>
			<data>
			DKEsKrqL++JMTAaGHp1XuTnF5y0cCPN5ikQtaY3rdZk=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/ossl_typ.h</key>
		<dict>
			<key>hash</key>
			<data>
			/hD+IfNYoPQpFE2wIl8jHi6BRVI=
			</data>
			<key>hash2</key>
			<data>
			RvVBocxJvwiQirO94F4mg/xW6iaMo+fy4nYoZT7LShQ=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/pem.h</key>
		<dict>
			<key>hash</key>
			<data>
			32ly0pNLPzdTYGECzCbGY6huGuw=
			</data>
			<key>hash2</key>
			<data>
			MtYgNtDDXQP+f33ar/48rq/PmE7Bbk237RnT4gLp17o=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/pem2.h</key>
		<dict>
			<key>hash</key>
			<data>
			t2jj9ipWiYEJZvgm7/w68FCv7xk=
			</data>
			<key>hash2</key>
			<data>
			aDPuVxISXRzo97UkN6dS5AwvR5MnaFmoIowN5x01w+A=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/pemerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			LPK/t/8oy0h9S1TUlxQnvG640ps=
			</data>
			<key>hash2</key>
			<data>
			2RxgFvPBunBoO5L95VUA7DNHd977ZIbEGwqcTkAqopU=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/pkcs12.h</key>
		<dict>
			<key>hash</key>
			<data>
			Wgtt5Ajf2I0Qe0Yw/C6/da673Vs=
			</data>
			<key>hash2</key>
			<data>
			l8Epxsmkk9fl068SPZYEDYfE5U/F5BrsRQgyyzKmNNY=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/pkcs12err.h</key>
		<dict>
			<key>hash</key>
			<data>
			iYGY/k8EiROz+H6/d3nIzlOnXHY=
			</data>
			<key>hash2</key>
			<data>
			tzQHJ6BIDzUYI7Zp9VzTmJwGXTc8jPb/h0W4NW9h1f8=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/pkcs7.h</key>
		<dict>
			<key>hash</key>
			<data>
			DSVKNOqJWVpNEj9g1afFP/AAHbs=
			</data>
			<key>hash2</key>
			<data>
			zcNQXJu5FopvrUNN2dmkmtYwua6SFr1mWxEFHgSnCbw=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/pkcs7err.h</key>
		<dict>
			<key>hash</key>
			<data>
			vbVkdgXZDNlmUBjHsDcvjoyWNTE=
			</data>
			<key>hash2</key>
			<data>
			7O4R6fvdqyD3igGACeai2vKH/13wBnkpixN/6Zltk4Y=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/rand.h</key>
		<dict>
			<key>hash</key>
			<data>
			BUI/kpuBobNUPlXrW/OkHeq9ClM=
			</data>
			<key>hash2</key>
			<data>
			WpGry90U3PL8YnYczFjulAcnTqVYwpL6d61iUgSsK9c=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/rand_drbg.h</key>
		<dict>
			<key>hash</key>
			<data>
			dZJZsnraib5Oxt12qSJ4bXxP3/w=
			</data>
			<key>hash2</key>
			<data>
			EjecwKfxaMv/jgiCjactwOh3c7xsO9FMS1dQYzm2H6U=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/randerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			/b3o6sjMLxkewJmLW7LeYF/10A0=
			</data>
			<key>hash2</key>
			<data>
			Qfzr+wdnr6A/PeJzLT8M5GzSNn7DSyAWzbXJ6EqaiZQ=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/rc2.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xzwvx1iv4W35klxWCpyRR35/Uwc=
			</data>
			<key>hash2</key>
			<data>
			7CuRlomLvEX/KrACBPk6byDJdCJVEPKQl61ppu7rzf4=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/rc4.h</key>
		<dict>
			<key>hash</key>
			<data>
			01mH39v8pvXIdzB3N/3bn0uJwVs=
			</data>
			<key>hash2</key>
			<data>
			b8AjRC9SQ0loXRPVCFStdzsSuMehU9ctYVqyfdSj1gk=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/rc5.h</key>
		<dict>
			<key>hash</key>
			<data>
			nVMdNFdbOheiSzNQjJ5v92LvEmI=
			</data>
			<key>hash2</key>
			<data>
			wjiVSh3yP1I2LW5fp43yx6WirW75U25InwojKV76DKs=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/ripemd.h</key>
		<dict>
			<key>hash</key>
			<data>
			UUNVXGUU1UnsGpXivIzpc/ZyFQs=
			</data>
			<key>hash2</key>
			<data>
			jHS5PBCp6Dq8F87TqAIa91BvOaD62rB7XbLU+uvPaLY=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/rsa.h</key>
		<dict>
			<key>hash</key>
			<data>
			BXOED7wh3elZhA9q8KoFrXffhUg=
			</data>
			<key>hash2</key>
			<data>
			UJxNuQgRlc9snl/UaDiQrrOVCfKZehmJ3VpXz0MDm8I=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/rsaerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			jrv7NJXCaI3Ppho9VNnyZFn9jxQ=
			</data>
			<key>hash2</key>
			<data>
			4DKpM7GkwT7JAddWGtI6zgGIG2DNzIavDkywoloqMlI=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/safestack.h</key>
		<dict>
			<key>hash</key>
			<data>
			Cd9WYSUSt9OnXah9XHF3QevCc+E=
			</data>
			<key>hash2</key>
			<data>
			V2Zdq7N/TwvYU1Odk8ZMuK3zf9lVLcn60hXMj0epkcQ=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/seed.h</key>
		<dict>
			<key>hash</key>
			<data>
			xIqmrORS9667meS1VHouBNO1FGI=
			</data>
			<key>hash2</key>
			<data>
			a/I3PfsQ3Fy8Ymzy/oa5scgjc9eZvda+E+7a99RUDVU=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/sha.h</key>
		<dict>
			<key>hash</key>
			<data>
			lu1HA4odImsyOAN6vcDKaHOxMrc=
			</data>
			<key>hash2</key>
			<data>
			C7Z0VIGsVrZ/RQ0JAz6BO/j2pfICXpDV61OeqxrV4yM=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/shim.h</key>
		<dict>
			<key>hash</key>
			<data>
			sMxnt7+rU4Gir29aFtcGAwjCdhs=
			</data>
			<key>hash2</key>
			<data>
			Ra475POOCRP20DOT0uDAvv/CkGhwldG3+hjPVXYZ3Ws=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/sm2.h</key>
		<dict>
			<key>hash</key>
			<data>
			J9BR3kk+bjp4DUXqRy3dTOnScEM=
			</data>
			<key>hash2</key>
			<data>
			K7FK9nwl8cJHxILCnHbOuVWycEoqpMFU3fiLPSY8/04=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/sm3.h</key>
		<dict>
			<key>hash</key>
			<data>
			H8hFoehBM7TTiupNtJpraPdF6TE=
			</data>
			<key>hash2</key>
			<data>
			RDSKyY74qd98l7DUslt8I5iPjP24RzWiakKvFsPoZmU=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/sm4.h</key>
		<dict>
			<key>hash</key>
			<data>
			u11NBly6iFRmqITEgK0HhoQVACA=
			</data>
			<key>hash2</key>
			<data>
			qj1uC4OsMfDYcvAZmMd1mc/ElaNU336uYHe5RKe5ja8=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/srp.h</key>
		<dict>
			<key>hash</key>
			<data>
			hs0FaZQgqkGx2ATJtkZLR3E7ajQ=
			</data>
			<key>hash2</key>
			<data>
			gqCL+ahm3sG33rZrQHdpDO4PbK+R6wATbF7tTo2UPQY=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/srtp.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xiev9lzpqvNNo1eXz+UZl4RZh+0=
			</data>
			<key>hash2</key>
			<data>
			5Q4t1d9qDbIZCRzRxnaKbTGe9khbFuHzYfzkMGeEdiY=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/ssl.h</key>
		<dict>
			<key>hash</key>
			<data>
			mhalYjN/qeuEF+l6WIVquvXvjOs=
			</data>
			<key>hash2</key>
			<data>
			Z+hVP6OlpyPzsPKff23VH/dbSj1OeZ6x2Y710a9gnlI=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/ssl2.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZUBF9zzQr/EnSx9hH0JCB0ETP8s=
			</data>
			<key>hash2</key>
			<data>
			f7VXoySIrUSiVCCr/4J5q9C9H0q3aOc9Ph1cLas2wMU=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/ssl3.h</key>
		<dict>
			<key>hash</key>
			<data>
			cA/eJmiVcOy62x5KksvM5fafytI=
			</data>
			<key>hash2</key>
			<data>
			0Ez+wqn52iqimfVYhCFeIAtJCm4KlCMlUmJki9im0cA=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/sslerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			4EiOgpXMaHQuyZqwDCHkMoCYXhw=
			</data>
			<key>hash2</key>
			<data>
			VKPHhOv6tjHSG7m32zzjgWeswCmeiSC1zm5Bmjc2kwo=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/stack.h</key>
		<dict>
			<key>hash</key>
			<data>
			HlhyAUEwF7fsEBjOpYfB+ZGw2SU=
			</data>
			<key>hash2</key>
			<data>
			RbqAO74UAH5JS7JLLKlU9jYu9wChBIDvo9LzrLFZ3uw=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/store.h</key>
		<dict>
			<key>hash</key>
			<data>
			f3f0erlSosQY7bDqrHLGzKe7YY4=
			</data>
			<key>hash2</key>
			<data>
			EQW60aMJ0xIqLPvcQJijPjPVDIEY5wuzMvO31u8ru1g=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/storeerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			kv/pt/4V9S0com6azZDBL4S2ef4=
			</data>
			<key>hash2</key>
			<data>
			BxxmvMA+1cKiTxlk9F6NGmM/PLSxg8cY3tPiUxL4xOA=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/symhacks.h</key>
		<dict>
			<key>hash</key>
			<data>
			9immsicIban6k2s+1MF7N3ITwzE=
			</data>
			<key>hash2</key>
			<data>
			DJ4CalkyokMuPN9977eJYQxCcgEOUeX/BHGAnqu3qi0=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/tls1.h</key>
		<dict>
			<key>hash</key>
			<data>
			snV9T38ogNzmGbcPr9Rn32lFHOo=
			</data>
			<key>hash2</key>
			<data>
			1BBMqHIDMrmFL0cl1GYKbPd6UrWH59luwmPplsHQri8=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/ts.h</key>
		<dict>
			<key>hash</key>
			<data>
			iWDV/jVLCzacuMrj1s833l3wAYY=
			</data>
			<key>hash2</key>
			<data>
			m9A568579rbnH8lmfkTgF/wMt8ecAjvhyWWJTmG3kjg=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/tserr.h</key>
		<dict>
			<key>hash</key>
			<data>
			PI3IsQNlWi/RwHMUbbL1VJDUvj0=
			</data>
			<key>hash2</key>
			<data>
			tuG7yMU+T3wFR2jexVJy0AHfv+54ioW6iwwGngjLvoU=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/txt_db.h</key>
		<dict>
			<key>hash</key>
			<data>
			NoTeslBGWtynVU+GDhyxyzXUdTE=
			</data>
			<key>hash2</key>
			<data>
			kDaWvVuZCFMPioV4/vR3IfR/P+hQe6x3YUc+kl6UIVk=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/ui.h</key>
		<dict>
			<key>hash</key>
			<data>
			++9liaOBXfJY40d+p4saEce2zpw=
			</data>
			<key>hash2</key>
			<data>
			9eo/XS576z6B24wjxuYY445RHCE/2ToR71G5rkrQNhk=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/uierr.h</key>
		<dict>
			<key>hash</key>
			<data>
			R61EyGTilaauZmnHry4jjFiN5ko=
			</data>
			<key>hash2</key>
			<data>
			uzk3fHAtl2VUfOlfnnWLRqVO5OJon4kgM88qTqV9XQs=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/whrlpool.h</key>
		<dict>
			<key>hash</key>
			<data>
			Uo0K/dGVqhsRUor/8CFpmWNaoHY=
			</data>
			<key>hash2</key>
			<data>
			OHUHItFzcIP6jK7fzNO85XQIDjVpIBDS+R3TA7FUr2k=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/x509.h</key>
		<dict>
			<key>hash</key>
			<data>
			5wWbXaF8/6/pZR0i8O64cxA2vYY=
			</data>
			<key>hash2</key>
			<data>
			8bizVtZJ2iQjbVqr36/gR7UWsgCNjV0J2l6Z+8S0qYw=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/x509_vfy.h</key>
		<dict>
			<key>hash</key>
			<data>
			+5MyYNetMS2YX3CRFqSzMEZc4TE=
			</data>
			<key>hash2</key>
			<data>
			9iGsTDiom9AQZWygCTd3MSan7zSLKaFLBMxexcCVtTE=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/x509err.h</key>
		<dict>
			<key>hash</key>
			<data>
			z4/1nUkbgTSuqRKSBA/+tS9zTD4=
			</data>
			<key>hash2</key>
			<data>
			YiSZuaqlRpV8gC2pruNnlMJMnH0uEFrmm0eEHs2sa3o=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/x509v3.h</key>
		<dict>
			<key>hash</key>
			<data>
			YhR8NOnXe50FuvmDc73j2oiOZGo=
			</data>
			<key>hash2</key>
			<data>
			p+eKGFhpfnWUC0FroftvThNXIuiMDCgK+Kl5l1xUQ9o=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Headers/x509v3err.h</key>
		<dict>
			<key>hash</key>
			<data>
			X1T6LneJ+WEeudGjNgNYP08RqMU=
			</data>
			<key>hash2</key>
			<data>
			PRfcyid81vY3OjCm4H4aLEQCSguYDMzMJTPXi/DCJ3Y=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			7SpMbr9ijEs9WZlUGf6R9fV2Rtw=
			</data>
			<key>hash2</key>
			<data>
			N/EKZ/VGlrSUPmgzk52r9jP5PhAoOeNT9n1LYXtkFxw=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			wSMjVnQJnXCQkrl0p+m5ijRHe3c=
			</data>
			<key>hash2</key>
			<data>
			Iyk3YE2iKlYEZnIdRRtNf4piXHBV2iLaIdaHtxJFxL8=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/OpenSSL</key>
		<dict>
			<key>hash</key>
			<data>
			uQAtzpTpeldYNEC6h3npAoL0V/U=
			</data>
			<key>hash2</key>
			<data>
			xZPnsWBKyC9IR2NgvFYYgfRO9VYgb02NA27z7LIUhPM=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			AL1dh5ctObXBjoBiabSJ86M3HQs=
			</data>
			<key>hash2</key>
			<data>
			WpuPwM3bECAbtHzCgEs/AExyUUdmItJb/E61TtRuEIQ=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash</key>
			<data>
			K2HAhfB7lo2i+OrS08dDRvaWQEA=
			</data>
			<key>hash2</key>
			<data>
			CNxIXoeNQ++ZAYuZLYpNST6V+8Gixj36m7OEMMZG8vo=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash</key>
			<data>
			LivNFy9yoqb1NZc0y0RjgYMqdYM=
			</data>
			<key>hash2</key>
			<data>
			sJBTjtPXegOlCm3Ps2Nhxa7ulbabs6YEjzSXcxoDZ1o=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash</key>
			<data>
			Kx5DIwGCOLbNQi3YdYRLynwdXFA=
			</data>
			<key>hash2</key>
			<data>
			DpP2779KzmkgLvPOVCrSXgcifOyl0F1BRLPz7IAAAOI=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			dhiboUceMhmHyBtiqyp+lM+rBWI=
			</data>
			<key>hash2</key>
			<data>
			2kLQTT/3KWhh4/sYeyQNl7ow1CZleaLXEw7kYKOlx9E=
			</data>
		</dict>
		<key>ios-arm64/OpenSSL.framework/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash</key>
			<data>
			AXBJap1yP/cLEqpaWWhiD10TFuQ=
			</data>
			<key>hash2</key>
			<data>
			mJbknGKDdyqPQz7sH90AagNtjqHyLvXf7l6I2JQJpg0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Headers</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Headers</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/OpenSSL</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/OpenSSL</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/OpenSSL.h</key>
		<dict>
			<key>hash</key>
			<data>
			TYMr1SKbMS+Pm94I583X46xmkDc=
			</data>
			<key>hash2</key>
			<data>
			cS7vM4LFBnKS8rmqpJq/kU1RjeE45QFwlMG1g2yq6dQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/aes.h</key>
		<dict>
			<key>hash</key>
			<data>
			/QHXsfp5KZBtt0hpQ+PGhRB5TQE=
			</data>
			<key>hash2</key>
			<data>
			9ron6H2X6lePAVQKj4S36rBdQsF4utznEtDMZo2KmYE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/asn1.h</key>
		<dict>
			<key>hash</key>
			<data>
			/JULnimbzTltoXNqIFMZvE15UFw=
			</data>
			<key>hash2</key>
			<data>
			hRMTsRdrqj1olqru6+9WIW69043Nq7ObPso2tmRTUrw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/asn1err.h</key>
		<dict>
			<key>hash</key>
			<data>
			vClK4nFx8VUb19HBjU1cQuNfemY=
			</data>
			<key>hash2</key>
			<data>
			uJsWIW3dNfAo9b/T/g208urWyUFed0m3EVsDk7Qn7y8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/asn1t.h</key>
		<dict>
			<key>hash</key>
			<data>
			LWrSPPRO1SZ6tiCyd9G2baHBT0g=
			</data>
			<key>hash2</key>
			<data>
			JwL1aTAiSLgijkkxd88KRp8Sfxzlx2vGhLyQ+9/EV0M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/async.h</key>
		<dict>
			<key>hash</key>
			<data>
			Du5KwAWX1O4Ld/G5CC+CgQkQU9M=
			</data>
			<key>hash2</key>
			<data>
			75bHFsrDk/LNbaMErwKRVezZ8CGGnNWkw0Hr3ySQNrw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/asyncerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			K5Jc+TuPuL/d4mP5tAhHKE2LRmc=
			</data>
			<key>hash2</key>
			<data>
			ysycqVGqUj0+1rnfI2YCJjOSWmcp0wzRNKBdL/5fsVY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/bio.h</key>
		<dict>
			<key>hash</key>
			<data>
			3e8z1vvxgbXPwmeg+1bRSmgfCG8=
			</data>
			<key>hash2</key>
			<data>
			eLBVVVlX7sK/tM5fkbJ73P33MSgCa+nOhfzKASb9zT0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/bioerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			N0rVDI9qQwyDEWvlqW1vGQrh0RU=
			</data>
			<key>hash2</key>
			<data>
			rSn+TvN/18SyVraQyvk3GnTimYH2VhuUQ22sVKYb90s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/blowfish.h</key>
		<dict>
			<key>hash</key>
			<data>
			BLqJpLWCl4Gl0DR4WO0luoyixMg=
			</data>
			<key>hash2</key>
			<data>
			gTssfP+Ii1twm5DU2lhK/vWbXOHqiIYX1oLxr0hXj68=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/bn.h</key>
		<dict>
			<key>hash</key>
			<data>
			zwKCI0fcYjm7MT9ZsUzGG4BDLTE=
			</data>
			<key>hash2</key>
			<data>
			j3mynFrUeRhqyq6WKlmNmGjcQ0/V+kmgCO7Pr/G5mMo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/bnerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			ybn9OmsYdCvnSKGlXSQQJ8b+Y44=
			</data>
			<key>hash2</key>
			<data>
			gJirlUUwfW4snMKK6ZXW/aDolZthZ8wD1ncWsCq7J/8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/buffer.h</key>
		<dict>
			<key>hash</key>
			<data>
			70BHajzy0JGUYwo7F+dpD0CeWrs=
			</data>
			<key>hash2</key>
			<data>
			pPopK5A8uMLOF2G6SYDPW9+2bcrcu1yK7MRbASvGTCM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/buffererr.h</key>
		<dict>
			<key>hash</key>
			<data>
			PSVdu9joU+POeEegSPtAPctXWyI=
			</data>
			<key>hash2</key>
			<data>
			ci9uqHcRt8DLbuKcFXYsKDnlrRe571ebboG2Eh8DXDA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/camellia.h</key>
		<dict>
			<key>hash</key>
			<data>
			R0cxfQe4VMejfw/FBnV5jlrTxS8=
			</data>
			<key>hash2</key>
			<data>
			VhvbLphUWOgJ5lbGDkvIPh1nB3Rt2LW628ELZBmNR3A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/cast.h</key>
		<dict>
			<key>hash</key>
			<data>
			tg9fweKyld2MF5c1iw7sEh5btDM=
			</data>
			<key>hash2</key>
			<data>
			oUxRoSmzrleV2v2YBlvguwy/ALTUUo6Frb9imWb0L1M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/cmac.h</key>
		<dict>
			<key>hash</key>
			<data>
			SsfJcPvnO3RZ7i+QyWeuyIBoFr4=
			</data>
			<key>hash2</key>
			<data>
			db+V0doRj/Hi8UPMt6Vk2UneRACXuXpJclwDl24We1I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/cms.h</key>
		<dict>
			<key>hash</key>
			<data>
			WhfyS38eVlM/0EjH+SRNVL1fhB0=
			</data>
			<key>hash2</key>
			<data>
			B+Swp3nJV++QJtacDLWmId3CBgr5Z+DdXpGshh/fvbI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/cmserr.h</key>
		<dict>
			<key>hash</key>
			<data>
			icv8DehBtI8fn3XrZvJ8GaK3Nm8=
			</data>
			<key>hash2</key>
			<data>
			fg/dq6hfs9ESYq7ixgJyWS3uGmI0pSqUsGuv0cClQ1A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/comp.h</key>
		<dict>
			<key>hash</key>
			<data>
			iXVcuTWmFCf9QLEmp9sNP8JQUw0=
			</data>
			<key>hash2</key>
			<data>
			mKh3xicj5gQuTkdAzRpud5nfdS2SNDhtc6KKCY5usxw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/comperr.h</key>
		<dict>
			<key>hash</key>
			<data>
			smXqglFOEgRt0CXetpBcXnWbZYI=
			</data>
			<key>hash2</key>
			<data>
			9kviBdCK8lVxh+wZwDZ4qp4p9m5CjeKc691izXw7VRU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/conf.h</key>
		<dict>
			<key>hash</key>
			<data>
			lY422quRPGGoMtvoZu73RVfY38s=
			</data>
			<key>hash2</key>
			<data>
			5UNB4wUj2twJ2CE2b1r7E8kobVQLNMGUVAaRjtWI+iE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/conf_api.h</key>
		<dict>
			<key>hash</key>
			<data>
			aEkI7MCNJGZ+SJxs514uMY1oW3s=
			</data>
			<key>hash2</key>
			<data>
			F1+GtptYsM0dpsmyfYerTk7VLzfcdreJXCEMQ0aVBOs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/conferr.h</key>
		<dict>
			<key>hash</key>
			<data>
			HhxJvI2jMpKEWKwpMC+m/3YD5tE=
			</data>
			<key>hash2</key>
			<data>
			q/4mSFzUOoDgxHbmKJeWEq0opjVXe69o7uR2hQQlpJg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/crypto.h</key>
		<dict>
			<key>hash</key>
			<data>
			9mlWjKYcqA63rFf05YOE/OnlLlE=
			</data>
			<key>hash2</key>
			<data>
			0s2E5pXmgFH+Z/p8+QHMuk+tzMcaazAJDfIKAbkdPsc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/cryptoerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			rvUn24W+SZM/y6GTW6NG1DW8KQo=
			</data>
			<key>hash2</key>
			<data>
			51LX09oypsAJzyZEUHJjZ/1p58KkwYXVgM5lACHWjno=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ct.h</key>
		<dict>
			<key>hash</key>
			<data>
			cpeLfPNApLUZ414BRmIcahaW0y0=
			</data>
			<key>hash2</key>
			<data>
			snqvDTlglQC0/OOztl9nUhFqyzBCDeHVOUMHSkgQQ/A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/cterr.h</key>
		<dict>
			<key>hash</key>
			<data>
			z6hnmIDS0tby0u7XWscijJOnipQ=
			</data>
			<key>hash2</key>
			<data>
			q25wtS40EkfEsheIOYD/HpjkRT0QILCwOM2/CWxKuKU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/des.h</key>
		<dict>
			<key>hash</key>
			<data>
			znOw/0Vq2B1QWQ5QlySAEIkrdwE=
			</data>
			<key>hash2</key>
			<data>
			PLaPu0mONOGeRvnUFWFZaracz3ix2L3y8xLoksBgAV8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/dh.h</key>
		<dict>
			<key>hash</key>
			<data>
			9zO15LRHFl5v1MTrCuS3Q2iGGu0=
			</data>
			<key>hash2</key>
			<data>
			RtDEiea4BjeSz52Bk/UnOPDY/spOuERV4KNZY21BqeA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/dherr.h</key>
		<dict>
			<key>hash</key>
			<data>
			PTwLUzp4LhfTwc/VMpRyT9YTuRU=
			</data>
			<key>hash2</key>
			<data>
			cpkNxez4V74yHIi5IVAshTZX+AnsU+r5GgEcCZqFdw0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/dsa.h</key>
		<dict>
			<key>hash</key>
			<data>
			AEYyYVu2fy7yPpiyRyjyypHNCKI=
			</data>
			<key>hash2</key>
			<data>
			FSXlrfJCZDp9NjElgDFPXOYeyt+CIjpJ/1wyIg14+bk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/dsaerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			jmOqsG1TSs1yTIm74Qstlz/KODg=
			</data>
			<key>hash2</key>
			<data>
			ZeqLR8slroLWETN81H2nwcXNuUUZgr7snYQUE9SpI6k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/dtls1.h</key>
		<dict>
			<key>hash</key>
			<data>
			fDDC+ixg7o1tDB4basD847J6+Zk=
			</data>
			<key>hash2</key>
			<data>
			cwTxfMmZi9fBZTmuml6lRfLgxkCG1eaIyroQS2p//sw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/e_os2.h</key>
		<dict>
			<key>hash</key>
			<data>
			lavqS62GkOuhGihyu+WOjVd92SM=
			</data>
			<key>hash2</key>
			<data>
			DWGWKc2z+15UMR6KP/YC+DjEpFMKVr4/0KgefosQy4Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ebcdic.h</key>
		<dict>
			<key>hash</key>
			<data>
			z5Fn9TbPaQo8zoY+Uwo/lSr9SJ8=
			</data>
			<key>hash2</key>
			<data>
			IonxTxHnWsc50RI2UdFuhB88dONtqkg8H+2fjFwURyA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ec.h</key>
		<dict>
			<key>hash</key>
			<data>
			VDanIs80uH9tNKL+PSMmYL0YIu4=
			</data>
			<key>hash2</key>
			<data>
			TmJAZFMZhlYHifhQnf0QvF7YLrZVmeA+07/tV75t8Zk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ecdh.h</key>
		<dict>
			<key>hash</key>
			<data>
			iXUqwjlcXCix2i1+T/zXRV4PU18=
			</data>
			<key>hash2</key>
			<data>
			lfuJrdO9MrbUPc8aUdGDm5Fed01xOK+vYY5pDvv0FLs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ecdsa.h</key>
		<dict>
			<key>hash</key>
			<data>
			iXUqwjlcXCix2i1+T/zXRV4PU18=
			</data>
			<key>hash2</key>
			<data>
			lfuJrdO9MrbUPc8aUdGDm5Fed01xOK+vYY5pDvv0FLs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ecerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			GyM9ZofjjCqJIe+IbO9j4p+HDZk=
			</data>
			<key>hash2</key>
			<data>
			haO2TAZEQgoYKVyAAaBrrV18PoTU+HHJJaG0X7ie8YI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/engine.h</key>
		<dict>
			<key>hash</key>
			<data>
			dS4eXHBYjCoSL3nDXCYuyZR9V9c=
			</data>
			<key>hash2</key>
			<data>
			qY3weurFeSk7GG6mHDXtzj7r3h/QPYVTVj8YAj3kfJU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/engineerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			H6oGO47GeHqzTYa913WPlF5cZzQ=
			</data>
			<key>hash2</key>
			<data>
			bz0ekbobeXGSlTDLisPvk0wLgqjNRZyvIk4kGj5a1uk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/err.h</key>
		<dict>
			<key>hash</key>
			<data>
			OZsmLZ7UryVhV7QY17J5cymnWlY=
			</data>
			<key>hash2</key>
			<data>
			FEcmPwqEDoE1xYbhbD2FjO6Tne3fT9kF45GGmAm02qI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/evp.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ue0l0jetFcNRP+2k4ve5l9kWsOw=
			</data>
			<key>hash2</key>
			<data>
			nZY73agYApiBcGY8z870N66mkEL0Ku86EatKIXuACu8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/evperr.h</key>
		<dict>
			<key>hash</key>
			<data>
			YPfW2qvVYScvnOl+vKfUlzf4pvg=
			</data>
			<key>hash2</key>
			<data>
			RdaLRi8lI5SIwCC+5fv2lwCwzXtNwrO6cBW93EmUdqQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/hmac.h</key>
		<dict>
			<key>hash</key>
			<data>
			oNXOm72CsIiR3i2/7dzmPfXHpnk=
			</data>
			<key>hash2</key>
			<data>
			djhsInPxBfVMV0ny/IVFc8NxqxhfkkjOKVp0jhJsqug=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/idea.h</key>
		<dict>
			<key>hash</key>
			<data>
			9OhfGjNERiWm+IaFZng3mj74a70=
			</data>
			<key>hash2</key>
			<data>
			uraCxcwGAnyC0SbtjmXX3Z3MdfpGTMvJXX8Wi9accy0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/kdf.h</key>
		<dict>
			<key>hash</key>
			<data>
			8bAA2Z5x72kQuKB9lY8B07YpOvA=
			</data>
			<key>hash2</key>
			<data>
			qq/UqWK6m58qJGIHbH97SZHZ2H5773HYfpMIEl4OKcA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/kdferr.h</key>
		<dict>
			<key>hash</key>
			<data>
			eBl7XkjAByV3fh1j+2ZSsSVdPd0=
			</data>
			<key>hash2</key>
			<data>
			5AbLyKinlWvLDgapSUVLSNJwIznK9KLmIawM3pwkY+M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/lhash.h</key>
		<dict>
			<key>hash</key>
			<data>
			gsp3p9sYfgyLm8w777aVi7LZzaA=
			</data>
			<key>hash2</key>
			<data>
			EDbiCroA4FhblrkaAK53ktwSUBFg4RfR+CSDPef+N1I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/md2.h</key>
		<dict>
			<key>hash</key>
			<data>
			SU5g+hFH8KXJwSElUE6qnz88XbQ=
			</data>
			<key>hash2</key>
			<data>
			nijwqNXiKXZJrymPXjIJ0y/pSG7bF5PMaYp1fJEnBmI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/md4.h</key>
		<dict>
			<key>hash</key>
			<data>
			NVmYVdXaFSHylpRJRh52LUqSAIY=
			</data>
			<key>hash2</key>
			<data>
			A9P8nc7sbBaNIZ659ATKCLR41VIWIoNLwkt9RZRXAuQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/md5.h</key>
		<dict>
			<key>hash</key>
			<data>
			8R2dids4HGec0BuJ5RjnI0sNAqs=
			</data>
			<key>hash2</key>
			<data>
			oGEqj35p47wWb8GGykS+HlsdAgtdqLW+Ah9z1IwnC4I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/mdc2.h</key>
		<dict>
			<key>hash</key>
			<data>
			ceP5kO5gOJDJGS7HrDRjpWWG2i4=
			</data>
			<key>hash2</key>
			<data>
			8TsB7AnEXCY0Zz0bmxp5rb1uwyvf+UKHMI4rsnQI5Tc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/modes.h</key>
		<dict>
			<key>hash</key>
			<data>
			oykGDYKdMkpM+dh+C5148xyIG9A=
			</data>
			<key>hash2</key>
			<data>
			9OUnmH4paib8HAb0+Ja6pfRXt+Z/lJV9F27tRpsP5gI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/obj_mac.h</key>
		<dict>
			<key>hash</key>
			<data>
			0xczM7tqfkjs0KVU5TgD3MmB0O8=
			</data>
			<key>hash2</key>
			<data>
			gevKIMw3DM9y8xA9EjHGpC4GRzC7IiYal9ht3Q57wIo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/objects.h</key>
		<dict>
			<key>hash</key>
			<data>
			1BKkzZQ86VAQYSD2wS3W+kAe8lU=
			</data>
			<key>hash2</key>
			<data>
			QBjXxU5vGcMjCvBj9NvIUj8UUFKKSvFal6Ch/z+zujg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/objectserr.h</key>
		<dict>
			<key>hash</key>
			<data>
			62CLZz09xeqGq4nUpt1HfuvRrbM=
			</data>
			<key>hash2</key>
			<data>
			DsczDBIq4rMXTfld3s6mX/ZhphUs0BklKd0UEe8/YqE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ocsp.h</key>
		<dict>
			<key>hash</key>
			<data>
			/vPykCbZnxZ/oD4x73y3vCFlfAg=
			</data>
			<key>hash2</key>
			<data>
			z7vDQ0tWxGcQgDJq64w9AH2rfSw2IdUUbBGro0cVi4A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ocsperr.h</key>
		<dict>
			<key>hash</key>
			<data>
			HzVTI0XxPNFGeTAYcha32Bf1qLw=
			</data>
			<key>hash2</key>
			<data>
			xzpXsZGWAf+MBPLcnGLb0TDvLTrjm7D83yW8nm6vMno=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/opensslconf.h</key>
		<dict>
			<key>hash</key>
			<data>
			yNGszHsg1rO96Mwyt+XRNUDMH3E=
			</data>
			<key>hash2</key>
			<data>
			TobY/gSgSFAftkR9MCoN++X6EOEwraLOKY4xULr1+Zo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/opensslv.h</key>
		<dict>
			<key>hash</key>
			<data>
			RV0inZj+UfBzOwFBUF97hvsSAjg=
			</data>
			<key>hash2</key>
			<data>
			DKEsKrqL++JMTAaGHp1XuTnF5y0cCPN5ikQtaY3rdZk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ossl_typ.h</key>
		<dict>
			<key>hash</key>
			<data>
			/hD+IfNYoPQpFE2wIl8jHi6BRVI=
			</data>
			<key>hash2</key>
			<data>
			RvVBocxJvwiQirO94F4mg/xW6iaMo+fy4nYoZT7LShQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/pem.h</key>
		<dict>
			<key>hash</key>
			<data>
			32ly0pNLPzdTYGECzCbGY6huGuw=
			</data>
			<key>hash2</key>
			<data>
			MtYgNtDDXQP+f33ar/48rq/PmE7Bbk237RnT4gLp17o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/pem2.h</key>
		<dict>
			<key>hash</key>
			<data>
			t2jj9ipWiYEJZvgm7/w68FCv7xk=
			</data>
			<key>hash2</key>
			<data>
			aDPuVxISXRzo97UkN6dS5AwvR5MnaFmoIowN5x01w+A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/pemerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			LPK/t/8oy0h9S1TUlxQnvG640ps=
			</data>
			<key>hash2</key>
			<data>
			2RxgFvPBunBoO5L95VUA7DNHd977ZIbEGwqcTkAqopU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/pkcs12.h</key>
		<dict>
			<key>hash</key>
			<data>
			Wgtt5Ajf2I0Qe0Yw/C6/da673Vs=
			</data>
			<key>hash2</key>
			<data>
			l8Epxsmkk9fl068SPZYEDYfE5U/F5BrsRQgyyzKmNNY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/pkcs12err.h</key>
		<dict>
			<key>hash</key>
			<data>
			iYGY/k8EiROz+H6/d3nIzlOnXHY=
			</data>
			<key>hash2</key>
			<data>
			tzQHJ6BIDzUYI7Zp9VzTmJwGXTc8jPb/h0W4NW9h1f8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/pkcs7.h</key>
		<dict>
			<key>hash</key>
			<data>
			DSVKNOqJWVpNEj9g1afFP/AAHbs=
			</data>
			<key>hash2</key>
			<data>
			zcNQXJu5FopvrUNN2dmkmtYwua6SFr1mWxEFHgSnCbw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/pkcs7err.h</key>
		<dict>
			<key>hash</key>
			<data>
			vbVkdgXZDNlmUBjHsDcvjoyWNTE=
			</data>
			<key>hash2</key>
			<data>
			7O4R6fvdqyD3igGACeai2vKH/13wBnkpixN/6Zltk4Y=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/rand.h</key>
		<dict>
			<key>hash</key>
			<data>
			BUI/kpuBobNUPlXrW/OkHeq9ClM=
			</data>
			<key>hash2</key>
			<data>
			WpGry90U3PL8YnYczFjulAcnTqVYwpL6d61iUgSsK9c=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/rand_drbg.h</key>
		<dict>
			<key>hash</key>
			<data>
			dZJZsnraib5Oxt12qSJ4bXxP3/w=
			</data>
			<key>hash2</key>
			<data>
			EjecwKfxaMv/jgiCjactwOh3c7xsO9FMS1dQYzm2H6U=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/randerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			/b3o6sjMLxkewJmLW7LeYF/10A0=
			</data>
			<key>hash2</key>
			<data>
			Qfzr+wdnr6A/PeJzLT8M5GzSNn7DSyAWzbXJ6EqaiZQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/rc2.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xzwvx1iv4W35klxWCpyRR35/Uwc=
			</data>
			<key>hash2</key>
			<data>
			7CuRlomLvEX/KrACBPk6byDJdCJVEPKQl61ppu7rzf4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/rc4.h</key>
		<dict>
			<key>hash</key>
			<data>
			01mH39v8pvXIdzB3N/3bn0uJwVs=
			</data>
			<key>hash2</key>
			<data>
			b8AjRC9SQ0loXRPVCFStdzsSuMehU9ctYVqyfdSj1gk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/rc5.h</key>
		<dict>
			<key>hash</key>
			<data>
			nVMdNFdbOheiSzNQjJ5v92LvEmI=
			</data>
			<key>hash2</key>
			<data>
			wjiVSh3yP1I2LW5fp43yx6WirW75U25InwojKV76DKs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ripemd.h</key>
		<dict>
			<key>hash</key>
			<data>
			UUNVXGUU1UnsGpXivIzpc/ZyFQs=
			</data>
			<key>hash2</key>
			<data>
			jHS5PBCp6Dq8F87TqAIa91BvOaD62rB7XbLU+uvPaLY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/rsa.h</key>
		<dict>
			<key>hash</key>
			<data>
			BXOED7wh3elZhA9q8KoFrXffhUg=
			</data>
			<key>hash2</key>
			<data>
			UJxNuQgRlc9snl/UaDiQrrOVCfKZehmJ3VpXz0MDm8I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/rsaerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			jrv7NJXCaI3Ppho9VNnyZFn9jxQ=
			</data>
			<key>hash2</key>
			<data>
			4DKpM7GkwT7JAddWGtI6zgGIG2DNzIavDkywoloqMlI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/safestack.h</key>
		<dict>
			<key>hash</key>
			<data>
			Cd9WYSUSt9OnXah9XHF3QevCc+E=
			</data>
			<key>hash2</key>
			<data>
			V2Zdq7N/TwvYU1Odk8ZMuK3zf9lVLcn60hXMj0epkcQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/seed.h</key>
		<dict>
			<key>hash</key>
			<data>
			xIqmrORS9667meS1VHouBNO1FGI=
			</data>
			<key>hash2</key>
			<data>
			a/I3PfsQ3Fy8Ymzy/oa5scgjc9eZvda+E+7a99RUDVU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/sha.h</key>
		<dict>
			<key>hash</key>
			<data>
			lu1HA4odImsyOAN6vcDKaHOxMrc=
			</data>
			<key>hash2</key>
			<data>
			C7Z0VIGsVrZ/RQ0JAz6BO/j2pfICXpDV61OeqxrV4yM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/shim.h</key>
		<dict>
			<key>hash</key>
			<data>
			sMxnt7+rU4Gir29aFtcGAwjCdhs=
			</data>
			<key>hash2</key>
			<data>
			Ra475POOCRP20DOT0uDAvv/CkGhwldG3+hjPVXYZ3Ws=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/sm2.h</key>
		<dict>
			<key>hash</key>
			<data>
			J9BR3kk+bjp4DUXqRy3dTOnScEM=
			</data>
			<key>hash2</key>
			<data>
			K7FK9nwl8cJHxILCnHbOuVWycEoqpMFU3fiLPSY8/04=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/sm3.h</key>
		<dict>
			<key>hash</key>
			<data>
			H8hFoehBM7TTiupNtJpraPdF6TE=
			</data>
			<key>hash2</key>
			<data>
			RDSKyY74qd98l7DUslt8I5iPjP24RzWiakKvFsPoZmU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/sm4.h</key>
		<dict>
			<key>hash</key>
			<data>
			u11NBly6iFRmqITEgK0HhoQVACA=
			</data>
			<key>hash2</key>
			<data>
			qj1uC4OsMfDYcvAZmMd1mc/ElaNU336uYHe5RKe5ja8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/srp.h</key>
		<dict>
			<key>hash</key>
			<data>
			hs0FaZQgqkGx2ATJtkZLR3E7ajQ=
			</data>
			<key>hash2</key>
			<data>
			gqCL+ahm3sG33rZrQHdpDO4PbK+R6wATbF7tTo2UPQY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/srtp.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xiev9lzpqvNNo1eXz+UZl4RZh+0=
			</data>
			<key>hash2</key>
			<data>
			5Q4t1d9qDbIZCRzRxnaKbTGe9khbFuHzYfzkMGeEdiY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ssl.h</key>
		<dict>
			<key>hash</key>
			<data>
			mhalYjN/qeuEF+l6WIVquvXvjOs=
			</data>
			<key>hash2</key>
			<data>
			Z+hVP6OlpyPzsPKff23VH/dbSj1OeZ6x2Y710a9gnlI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ssl2.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZUBF9zzQr/EnSx9hH0JCB0ETP8s=
			</data>
			<key>hash2</key>
			<data>
			f7VXoySIrUSiVCCr/4J5q9C9H0q3aOc9Ph1cLas2wMU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ssl3.h</key>
		<dict>
			<key>hash</key>
			<data>
			cA/eJmiVcOy62x5KksvM5fafytI=
			</data>
			<key>hash2</key>
			<data>
			0Ez+wqn52iqimfVYhCFeIAtJCm4KlCMlUmJki9im0cA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/sslerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			4EiOgpXMaHQuyZqwDCHkMoCYXhw=
			</data>
			<key>hash2</key>
			<data>
			VKPHhOv6tjHSG7m32zzjgWeswCmeiSC1zm5Bmjc2kwo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/stack.h</key>
		<dict>
			<key>hash</key>
			<data>
			HlhyAUEwF7fsEBjOpYfB+ZGw2SU=
			</data>
			<key>hash2</key>
			<data>
			RbqAO74UAH5JS7JLLKlU9jYu9wChBIDvo9LzrLFZ3uw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/store.h</key>
		<dict>
			<key>hash</key>
			<data>
			f3f0erlSosQY7bDqrHLGzKe7YY4=
			</data>
			<key>hash2</key>
			<data>
			EQW60aMJ0xIqLPvcQJijPjPVDIEY5wuzMvO31u8ru1g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/storeerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			kv/pt/4V9S0com6azZDBL4S2ef4=
			</data>
			<key>hash2</key>
			<data>
			BxxmvMA+1cKiTxlk9F6NGmM/PLSxg8cY3tPiUxL4xOA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/symhacks.h</key>
		<dict>
			<key>hash</key>
			<data>
			9immsicIban6k2s+1MF7N3ITwzE=
			</data>
			<key>hash2</key>
			<data>
			DJ4CalkyokMuPN9977eJYQxCcgEOUeX/BHGAnqu3qi0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/tls1.h</key>
		<dict>
			<key>hash</key>
			<data>
			snV9T38ogNzmGbcPr9Rn32lFHOo=
			</data>
			<key>hash2</key>
			<data>
			1BBMqHIDMrmFL0cl1GYKbPd6UrWH59luwmPplsHQri8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ts.h</key>
		<dict>
			<key>hash</key>
			<data>
			iWDV/jVLCzacuMrj1s833l3wAYY=
			</data>
			<key>hash2</key>
			<data>
			m9A568579rbnH8lmfkTgF/wMt8ecAjvhyWWJTmG3kjg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/tserr.h</key>
		<dict>
			<key>hash</key>
			<data>
			PI3IsQNlWi/RwHMUbbL1VJDUvj0=
			</data>
			<key>hash2</key>
			<data>
			tuG7yMU+T3wFR2jexVJy0AHfv+54ioW6iwwGngjLvoU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/txt_db.h</key>
		<dict>
			<key>hash</key>
			<data>
			NoTeslBGWtynVU+GDhyxyzXUdTE=
			</data>
			<key>hash2</key>
			<data>
			kDaWvVuZCFMPioV4/vR3IfR/P+hQe6x3YUc+kl6UIVk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/ui.h</key>
		<dict>
			<key>hash</key>
			<data>
			++9liaOBXfJY40d+p4saEce2zpw=
			</data>
			<key>hash2</key>
			<data>
			9eo/XS576z6B24wjxuYY445RHCE/2ToR71G5rkrQNhk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/uierr.h</key>
		<dict>
			<key>hash</key>
			<data>
			R61EyGTilaauZmnHry4jjFiN5ko=
			</data>
			<key>hash2</key>
			<data>
			uzk3fHAtl2VUfOlfnnWLRqVO5OJon4kgM88qTqV9XQs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/whrlpool.h</key>
		<dict>
			<key>hash</key>
			<data>
			Uo0K/dGVqhsRUor/8CFpmWNaoHY=
			</data>
			<key>hash2</key>
			<data>
			OHUHItFzcIP6jK7fzNO85XQIDjVpIBDS+R3TA7FUr2k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/x509.h</key>
		<dict>
			<key>hash</key>
			<data>
			5wWbXaF8/6/pZR0i8O64cxA2vYY=
			</data>
			<key>hash2</key>
			<data>
			8bizVtZJ2iQjbVqr36/gR7UWsgCNjV0J2l6Z+8S0qYw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/x509_vfy.h</key>
		<dict>
			<key>hash</key>
			<data>
			+5MyYNetMS2YX3CRFqSzMEZc4TE=
			</data>
			<key>hash2</key>
			<data>
			9iGsTDiom9AQZWygCTd3MSan7zSLKaFLBMxexcCVtTE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/x509err.h</key>
		<dict>
			<key>hash</key>
			<data>
			z4/1nUkbgTSuqRKSBA/+tS9zTD4=
			</data>
			<key>hash2</key>
			<data>
			YiSZuaqlRpV8gC2pruNnlMJMnH0uEFrmm0eEHs2sa3o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/x509v3.h</key>
		<dict>
			<key>hash</key>
			<data>
			YhR8NOnXe50FuvmDc73j2oiOZGo=
			</data>
			<key>hash2</key>
			<data>
			p+eKGFhpfnWUC0FroftvThNXIuiMDCgK+Kl5l1xUQ9o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Headers/x509v3err.h</key>
		<dict>
			<key>hash</key>
			<data>
			X1T6LneJ+WEeudGjNgNYP08RqMU=
			</data>
			<key>hash2</key>
			<data>
			PRfcyid81vY3OjCm4H4aLEQCSguYDMzMJTPXi/DCJ3Y=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			wSMjVnQJnXCQkrl0p+m5ijRHe3c=
			</data>
			<key>hash2</key>
			<data>
			Iyk3YE2iKlYEZnIdRRtNf4piXHBV2iLaIdaHtxJFxL8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/OpenSSL</key>
		<dict>
			<key>hash</key>
			<data>
			cnIBb+QkjFdekqhD73pTvwC3zPM=
			</data>
			<key>hash2</key>
			<data>
			tnPQOFP0ooDuzcjd93Nmj0siL5NOe+BmUhXWYNJEn80=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			8HGXjkay6IFtM2LDnzd3SY7GTWo=
			</data>
			<key>hash2</key>
			<data>
			Rgtl4r5+SZBr3GGxHYV2TXz8iUXcD37NYiSZTl3oxMc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			AL1dh5ctObXBjoBiabSJ86M3HQs=
			</data>
			<key>hash2</key>
			<data>
			WpuPwM3bECAbtHzCgEs/AExyUUdmItJb/E61TtRuEIQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash</key>
			<data>
			YyfuX/muGBjIohq9hzzjy210Vro=
			</data>
			<key>hash2</key>
			<data>
			64BVktVmlwZk9H/ZV/6Aaw8NJ4LrDp3QKhN0JYJPQgw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash</key>
			<data>
			LivNFy9yoqb1NZc0y0RjgYMqdYM=
			</data>
			<key>hash2</key>
			<data>
			sJBTjtPXegOlCm3Ps2Nhxa7ulbabs6YEjzSXcxoDZ1o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash</key>
			<data>
			w/iugVcTIRZ9zywyochI66lNZIA=
			</data>
			<key>hash2</key>
			<data>
			i8x8bzQh1i9HPiv375e/fMahWH9E92ikP5PiPsvzrVE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			YUVuK7fxmxCwROOppC8PTDTP+5U=
			</data>
			<key>hash2</key>
			<data>
			sP1LB1In7y5SXP0zf32Zu1/+xS3LZ3lOiSOsqrYQUbU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/A/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash</key>
			<data>
			TKuI2RAsdFMM4MO1dIqopQxg6Jg=
			</data>
			<key>hash2</key>
			<data>
			aR4+OqtNcAGaMS4LiUIl8mKD1UEBwfE+rZKpdE1bDa0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/OpenSSL.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/OpenSSL.h</key>
		<dict>
			<key>hash</key>
			<data>
			tx85aV3apVi3nD5Fm04TxZsWR60=
			</data>
			<key>hash2</key>
			<data>
			y9iHB5RbKQCJ91zlKRcuy6M4Afhl4e/d2Pz3jiv1GOI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/aes.h</key>
		<dict>
			<key>hash</key>
			<data>
			/QHXsfp5KZBtt0hpQ+PGhRB5TQE=
			</data>
			<key>hash2</key>
			<data>
			9ron6H2X6lePAVQKj4S36rBdQsF4utznEtDMZo2KmYE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/asn1.h</key>
		<dict>
			<key>hash</key>
			<data>
			/JULnimbzTltoXNqIFMZvE15UFw=
			</data>
			<key>hash2</key>
			<data>
			hRMTsRdrqj1olqru6+9WIW69043Nq7ObPso2tmRTUrw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/asn1err.h</key>
		<dict>
			<key>hash</key>
			<data>
			vClK4nFx8VUb19HBjU1cQuNfemY=
			</data>
			<key>hash2</key>
			<data>
			uJsWIW3dNfAo9b/T/g208urWyUFed0m3EVsDk7Qn7y8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/asn1t.h</key>
		<dict>
			<key>hash</key>
			<data>
			LWrSPPRO1SZ6tiCyd9G2baHBT0g=
			</data>
			<key>hash2</key>
			<data>
			JwL1aTAiSLgijkkxd88KRp8Sfxzlx2vGhLyQ+9/EV0M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/async.h</key>
		<dict>
			<key>hash</key>
			<data>
			Du5KwAWX1O4Ld/G5CC+CgQkQU9M=
			</data>
			<key>hash2</key>
			<data>
			75bHFsrDk/LNbaMErwKRVezZ8CGGnNWkw0Hr3ySQNrw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/asyncerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			K5Jc+TuPuL/d4mP5tAhHKE2LRmc=
			</data>
			<key>hash2</key>
			<data>
			ysycqVGqUj0+1rnfI2YCJjOSWmcp0wzRNKBdL/5fsVY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/bio.h</key>
		<dict>
			<key>hash</key>
			<data>
			3e8z1vvxgbXPwmeg+1bRSmgfCG8=
			</data>
			<key>hash2</key>
			<data>
			eLBVVVlX7sK/tM5fkbJ73P33MSgCa+nOhfzKASb9zT0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/bioerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			N0rVDI9qQwyDEWvlqW1vGQrh0RU=
			</data>
			<key>hash2</key>
			<data>
			rSn+TvN/18SyVraQyvk3GnTimYH2VhuUQ22sVKYb90s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/blowfish.h</key>
		<dict>
			<key>hash</key>
			<data>
			BLqJpLWCl4Gl0DR4WO0luoyixMg=
			</data>
			<key>hash2</key>
			<data>
			gTssfP+Ii1twm5DU2lhK/vWbXOHqiIYX1oLxr0hXj68=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/bn.h</key>
		<dict>
			<key>hash</key>
			<data>
			zwKCI0fcYjm7MT9ZsUzGG4BDLTE=
			</data>
			<key>hash2</key>
			<data>
			j3mynFrUeRhqyq6WKlmNmGjcQ0/V+kmgCO7Pr/G5mMo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/bnerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			ybn9OmsYdCvnSKGlXSQQJ8b+Y44=
			</data>
			<key>hash2</key>
			<data>
			gJirlUUwfW4snMKK6ZXW/aDolZthZ8wD1ncWsCq7J/8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/buffer.h</key>
		<dict>
			<key>hash</key>
			<data>
			70BHajzy0JGUYwo7F+dpD0CeWrs=
			</data>
			<key>hash2</key>
			<data>
			pPopK5A8uMLOF2G6SYDPW9+2bcrcu1yK7MRbASvGTCM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/buffererr.h</key>
		<dict>
			<key>hash</key>
			<data>
			PSVdu9joU+POeEegSPtAPctXWyI=
			</data>
			<key>hash2</key>
			<data>
			ci9uqHcRt8DLbuKcFXYsKDnlrRe571ebboG2Eh8DXDA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/camellia.h</key>
		<dict>
			<key>hash</key>
			<data>
			R0cxfQe4VMejfw/FBnV5jlrTxS8=
			</data>
			<key>hash2</key>
			<data>
			VhvbLphUWOgJ5lbGDkvIPh1nB3Rt2LW628ELZBmNR3A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/cast.h</key>
		<dict>
			<key>hash</key>
			<data>
			tg9fweKyld2MF5c1iw7sEh5btDM=
			</data>
			<key>hash2</key>
			<data>
			oUxRoSmzrleV2v2YBlvguwy/ALTUUo6Frb9imWb0L1M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/cmac.h</key>
		<dict>
			<key>hash</key>
			<data>
			SsfJcPvnO3RZ7i+QyWeuyIBoFr4=
			</data>
			<key>hash2</key>
			<data>
			db+V0doRj/Hi8UPMt6Vk2UneRACXuXpJclwDl24We1I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/cms.h</key>
		<dict>
			<key>hash</key>
			<data>
			WhfyS38eVlM/0EjH+SRNVL1fhB0=
			</data>
			<key>hash2</key>
			<data>
			B+Swp3nJV++QJtacDLWmId3CBgr5Z+DdXpGshh/fvbI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/cmserr.h</key>
		<dict>
			<key>hash</key>
			<data>
			icv8DehBtI8fn3XrZvJ8GaK3Nm8=
			</data>
			<key>hash2</key>
			<data>
			fg/dq6hfs9ESYq7ixgJyWS3uGmI0pSqUsGuv0cClQ1A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/comp.h</key>
		<dict>
			<key>hash</key>
			<data>
			iXVcuTWmFCf9QLEmp9sNP8JQUw0=
			</data>
			<key>hash2</key>
			<data>
			mKh3xicj5gQuTkdAzRpud5nfdS2SNDhtc6KKCY5usxw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/comperr.h</key>
		<dict>
			<key>hash</key>
			<data>
			smXqglFOEgRt0CXetpBcXnWbZYI=
			</data>
			<key>hash2</key>
			<data>
			9kviBdCK8lVxh+wZwDZ4qp4p9m5CjeKc691izXw7VRU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/conf.h</key>
		<dict>
			<key>hash</key>
			<data>
			lY422quRPGGoMtvoZu73RVfY38s=
			</data>
			<key>hash2</key>
			<data>
			5UNB4wUj2twJ2CE2b1r7E8kobVQLNMGUVAaRjtWI+iE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/conf_api.h</key>
		<dict>
			<key>hash</key>
			<data>
			aEkI7MCNJGZ+SJxs514uMY1oW3s=
			</data>
			<key>hash2</key>
			<data>
			F1+GtptYsM0dpsmyfYerTk7VLzfcdreJXCEMQ0aVBOs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/conferr.h</key>
		<dict>
			<key>hash</key>
			<data>
			HhxJvI2jMpKEWKwpMC+m/3YD5tE=
			</data>
			<key>hash2</key>
			<data>
			q/4mSFzUOoDgxHbmKJeWEq0opjVXe69o7uR2hQQlpJg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/crypto.h</key>
		<dict>
			<key>hash</key>
			<data>
			9mlWjKYcqA63rFf05YOE/OnlLlE=
			</data>
			<key>hash2</key>
			<data>
			0s2E5pXmgFH+Z/p8+QHMuk+tzMcaazAJDfIKAbkdPsc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/cryptoerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			rvUn24W+SZM/y6GTW6NG1DW8KQo=
			</data>
			<key>hash2</key>
			<data>
			51LX09oypsAJzyZEUHJjZ/1p58KkwYXVgM5lACHWjno=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ct.h</key>
		<dict>
			<key>hash</key>
			<data>
			cpeLfPNApLUZ414BRmIcahaW0y0=
			</data>
			<key>hash2</key>
			<data>
			snqvDTlglQC0/OOztl9nUhFqyzBCDeHVOUMHSkgQQ/A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/cterr.h</key>
		<dict>
			<key>hash</key>
			<data>
			z6hnmIDS0tby0u7XWscijJOnipQ=
			</data>
			<key>hash2</key>
			<data>
			q25wtS40EkfEsheIOYD/HpjkRT0QILCwOM2/CWxKuKU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/des.h</key>
		<dict>
			<key>hash</key>
			<data>
			znOw/0Vq2B1QWQ5QlySAEIkrdwE=
			</data>
			<key>hash2</key>
			<data>
			PLaPu0mONOGeRvnUFWFZaracz3ix2L3y8xLoksBgAV8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/dh.h</key>
		<dict>
			<key>hash</key>
			<data>
			9zO15LRHFl5v1MTrCuS3Q2iGGu0=
			</data>
			<key>hash2</key>
			<data>
			RtDEiea4BjeSz52Bk/UnOPDY/spOuERV4KNZY21BqeA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/dherr.h</key>
		<dict>
			<key>hash</key>
			<data>
			PTwLUzp4LhfTwc/VMpRyT9YTuRU=
			</data>
			<key>hash2</key>
			<data>
			cpkNxez4V74yHIi5IVAshTZX+AnsU+r5GgEcCZqFdw0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/dsa.h</key>
		<dict>
			<key>hash</key>
			<data>
			AEYyYVu2fy7yPpiyRyjyypHNCKI=
			</data>
			<key>hash2</key>
			<data>
			FSXlrfJCZDp9NjElgDFPXOYeyt+CIjpJ/1wyIg14+bk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/dsaerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			jmOqsG1TSs1yTIm74Qstlz/KODg=
			</data>
			<key>hash2</key>
			<data>
			ZeqLR8slroLWETN81H2nwcXNuUUZgr7snYQUE9SpI6k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/dtls1.h</key>
		<dict>
			<key>hash</key>
			<data>
			fDDC+ixg7o1tDB4basD847J6+Zk=
			</data>
			<key>hash2</key>
			<data>
			cwTxfMmZi9fBZTmuml6lRfLgxkCG1eaIyroQS2p//sw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/e_os2.h</key>
		<dict>
			<key>hash</key>
			<data>
			lavqS62GkOuhGihyu+WOjVd92SM=
			</data>
			<key>hash2</key>
			<data>
			DWGWKc2z+15UMR6KP/YC+DjEpFMKVr4/0KgefosQy4Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ebcdic.h</key>
		<dict>
			<key>hash</key>
			<data>
			z5Fn9TbPaQo8zoY+Uwo/lSr9SJ8=
			</data>
			<key>hash2</key>
			<data>
			IonxTxHnWsc50RI2UdFuhB88dONtqkg8H+2fjFwURyA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ec.h</key>
		<dict>
			<key>hash</key>
			<data>
			VDanIs80uH9tNKL+PSMmYL0YIu4=
			</data>
			<key>hash2</key>
			<data>
			TmJAZFMZhlYHifhQnf0QvF7YLrZVmeA+07/tV75t8Zk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ecdh.h</key>
		<dict>
			<key>hash</key>
			<data>
			iXUqwjlcXCix2i1+T/zXRV4PU18=
			</data>
			<key>hash2</key>
			<data>
			lfuJrdO9MrbUPc8aUdGDm5Fed01xOK+vYY5pDvv0FLs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ecdsa.h</key>
		<dict>
			<key>hash</key>
			<data>
			iXUqwjlcXCix2i1+T/zXRV4PU18=
			</data>
			<key>hash2</key>
			<data>
			lfuJrdO9MrbUPc8aUdGDm5Fed01xOK+vYY5pDvv0FLs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ecerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			GyM9ZofjjCqJIe+IbO9j4p+HDZk=
			</data>
			<key>hash2</key>
			<data>
			haO2TAZEQgoYKVyAAaBrrV18PoTU+HHJJaG0X7ie8YI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/engine.h</key>
		<dict>
			<key>hash</key>
			<data>
			dS4eXHBYjCoSL3nDXCYuyZR9V9c=
			</data>
			<key>hash2</key>
			<data>
			qY3weurFeSk7GG6mHDXtzj7r3h/QPYVTVj8YAj3kfJU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/engineerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			H6oGO47GeHqzTYa913WPlF5cZzQ=
			</data>
			<key>hash2</key>
			<data>
			bz0ekbobeXGSlTDLisPvk0wLgqjNRZyvIk4kGj5a1uk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/err.h</key>
		<dict>
			<key>hash</key>
			<data>
			OZsmLZ7UryVhV7QY17J5cymnWlY=
			</data>
			<key>hash2</key>
			<data>
			FEcmPwqEDoE1xYbhbD2FjO6Tne3fT9kF45GGmAm02qI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/evp.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ue0l0jetFcNRP+2k4ve5l9kWsOw=
			</data>
			<key>hash2</key>
			<data>
			nZY73agYApiBcGY8z870N66mkEL0Ku86EatKIXuACu8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/evperr.h</key>
		<dict>
			<key>hash</key>
			<data>
			YPfW2qvVYScvnOl+vKfUlzf4pvg=
			</data>
			<key>hash2</key>
			<data>
			RdaLRi8lI5SIwCC+5fv2lwCwzXtNwrO6cBW93EmUdqQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/hmac.h</key>
		<dict>
			<key>hash</key>
			<data>
			oNXOm72CsIiR3i2/7dzmPfXHpnk=
			</data>
			<key>hash2</key>
			<data>
			djhsInPxBfVMV0ny/IVFc8NxqxhfkkjOKVp0jhJsqug=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/idea.h</key>
		<dict>
			<key>hash</key>
			<data>
			9OhfGjNERiWm+IaFZng3mj74a70=
			</data>
			<key>hash2</key>
			<data>
			uraCxcwGAnyC0SbtjmXX3Z3MdfpGTMvJXX8Wi9accy0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/kdf.h</key>
		<dict>
			<key>hash</key>
			<data>
			8bAA2Z5x72kQuKB9lY8B07YpOvA=
			</data>
			<key>hash2</key>
			<data>
			qq/UqWK6m58qJGIHbH97SZHZ2H5773HYfpMIEl4OKcA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/kdferr.h</key>
		<dict>
			<key>hash</key>
			<data>
			eBl7XkjAByV3fh1j+2ZSsSVdPd0=
			</data>
			<key>hash2</key>
			<data>
			5AbLyKinlWvLDgapSUVLSNJwIznK9KLmIawM3pwkY+M=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/lhash.h</key>
		<dict>
			<key>hash</key>
			<data>
			gsp3p9sYfgyLm8w777aVi7LZzaA=
			</data>
			<key>hash2</key>
			<data>
			EDbiCroA4FhblrkaAK53ktwSUBFg4RfR+CSDPef+N1I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/md2.h</key>
		<dict>
			<key>hash</key>
			<data>
			SU5g+hFH8KXJwSElUE6qnz88XbQ=
			</data>
			<key>hash2</key>
			<data>
			nijwqNXiKXZJrymPXjIJ0y/pSG7bF5PMaYp1fJEnBmI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/md4.h</key>
		<dict>
			<key>hash</key>
			<data>
			NVmYVdXaFSHylpRJRh52LUqSAIY=
			</data>
			<key>hash2</key>
			<data>
			A9P8nc7sbBaNIZ659ATKCLR41VIWIoNLwkt9RZRXAuQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/md5.h</key>
		<dict>
			<key>hash</key>
			<data>
			8R2dids4HGec0BuJ5RjnI0sNAqs=
			</data>
			<key>hash2</key>
			<data>
			oGEqj35p47wWb8GGykS+HlsdAgtdqLW+Ah9z1IwnC4I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/mdc2.h</key>
		<dict>
			<key>hash</key>
			<data>
			ceP5kO5gOJDJGS7HrDRjpWWG2i4=
			</data>
			<key>hash2</key>
			<data>
			8TsB7AnEXCY0Zz0bmxp5rb1uwyvf+UKHMI4rsnQI5Tc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/modes.h</key>
		<dict>
			<key>hash</key>
			<data>
			oykGDYKdMkpM+dh+C5148xyIG9A=
			</data>
			<key>hash2</key>
			<data>
			9OUnmH4paib8HAb0+Ja6pfRXt+Z/lJV9F27tRpsP5gI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/obj_mac.h</key>
		<dict>
			<key>hash</key>
			<data>
			0xczM7tqfkjs0KVU5TgD3MmB0O8=
			</data>
			<key>hash2</key>
			<data>
			gevKIMw3DM9y8xA9EjHGpC4GRzC7IiYal9ht3Q57wIo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/objects.h</key>
		<dict>
			<key>hash</key>
			<data>
			1BKkzZQ86VAQYSD2wS3W+kAe8lU=
			</data>
			<key>hash2</key>
			<data>
			QBjXxU5vGcMjCvBj9NvIUj8UUFKKSvFal6Ch/z+zujg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/objectserr.h</key>
		<dict>
			<key>hash</key>
			<data>
			62CLZz09xeqGq4nUpt1HfuvRrbM=
			</data>
			<key>hash2</key>
			<data>
			DsczDBIq4rMXTfld3s6mX/ZhphUs0BklKd0UEe8/YqE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ocsp.h</key>
		<dict>
			<key>hash</key>
			<data>
			/vPykCbZnxZ/oD4x73y3vCFlfAg=
			</data>
			<key>hash2</key>
			<data>
			z7vDQ0tWxGcQgDJq64w9AH2rfSw2IdUUbBGro0cVi4A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ocsperr.h</key>
		<dict>
			<key>hash</key>
			<data>
			HzVTI0XxPNFGeTAYcha32Bf1qLw=
			</data>
			<key>hash2</key>
			<data>
			xzpXsZGWAf+MBPLcnGLb0TDvLTrjm7D83yW8nm6vMno=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/opensslconf.h</key>
		<dict>
			<key>hash</key>
			<data>
			J4AlQEAw2IES9GWsNFdc2SJQhl4=
			</data>
			<key>hash2</key>
			<data>
			RIHfnM1BT/QKAaWgYC9C/FB85EqRsXPIh19peU69SLo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/opensslv.h</key>
		<dict>
			<key>hash</key>
			<data>
			RV0inZj+UfBzOwFBUF97hvsSAjg=
			</data>
			<key>hash2</key>
			<data>
			DKEsKrqL++JMTAaGHp1XuTnF5y0cCPN5ikQtaY3rdZk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ossl_typ.h</key>
		<dict>
			<key>hash</key>
			<data>
			/hD+IfNYoPQpFE2wIl8jHi6BRVI=
			</data>
			<key>hash2</key>
			<data>
			RvVBocxJvwiQirO94F4mg/xW6iaMo+fy4nYoZT7LShQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/pem.h</key>
		<dict>
			<key>hash</key>
			<data>
			32ly0pNLPzdTYGECzCbGY6huGuw=
			</data>
			<key>hash2</key>
			<data>
			MtYgNtDDXQP+f33ar/48rq/PmE7Bbk237RnT4gLp17o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/pem2.h</key>
		<dict>
			<key>hash</key>
			<data>
			t2jj9ipWiYEJZvgm7/w68FCv7xk=
			</data>
			<key>hash2</key>
			<data>
			aDPuVxISXRzo97UkN6dS5AwvR5MnaFmoIowN5x01w+A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/pemerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			LPK/t/8oy0h9S1TUlxQnvG640ps=
			</data>
			<key>hash2</key>
			<data>
			2RxgFvPBunBoO5L95VUA7DNHd977ZIbEGwqcTkAqopU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/pkcs12.h</key>
		<dict>
			<key>hash</key>
			<data>
			Wgtt5Ajf2I0Qe0Yw/C6/da673Vs=
			</data>
			<key>hash2</key>
			<data>
			l8Epxsmkk9fl068SPZYEDYfE5U/F5BrsRQgyyzKmNNY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/pkcs12err.h</key>
		<dict>
			<key>hash</key>
			<data>
			iYGY/k8EiROz+H6/d3nIzlOnXHY=
			</data>
			<key>hash2</key>
			<data>
			tzQHJ6BIDzUYI7Zp9VzTmJwGXTc8jPb/h0W4NW9h1f8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/pkcs7.h</key>
		<dict>
			<key>hash</key>
			<data>
			DSVKNOqJWVpNEj9g1afFP/AAHbs=
			</data>
			<key>hash2</key>
			<data>
			zcNQXJu5FopvrUNN2dmkmtYwua6SFr1mWxEFHgSnCbw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/pkcs7err.h</key>
		<dict>
			<key>hash</key>
			<data>
			vbVkdgXZDNlmUBjHsDcvjoyWNTE=
			</data>
			<key>hash2</key>
			<data>
			7O4R6fvdqyD3igGACeai2vKH/13wBnkpixN/6Zltk4Y=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/rand.h</key>
		<dict>
			<key>hash</key>
			<data>
			BUI/kpuBobNUPlXrW/OkHeq9ClM=
			</data>
			<key>hash2</key>
			<data>
			WpGry90U3PL8YnYczFjulAcnTqVYwpL6d61iUgSsK9c=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/rand_drbg.h</key>
		<dict>
			<key>hash</key>
			<data>
			dZJZsnraib5Oxt12qSJ4bXxP3/w=
			</data>
			<key>hash2</key>
			<data>
			EjecwKfxaMv/jgiCjactwOh3c7xsO9FMS1dQYzm2H6U=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/randerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			/b3o6sjMLxkewJmLW7LeYF/10A0=
			</data>
			<key>hash2</key>
			<data>
			Qfzr+wdnr6A/PeJzLT8M5GzSNn7DSyAWzbXJ6EqaiZQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/rc2.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xzwvx1iv4W35klxWCpyRR35/Uwc=
			</data>
			<key>hash2</key>
			<data>
			7CuRlomLvEX/KrACBPk6byDJdCJVEPKQl61ppu7rzf4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/rc4.h</key>
		<dict>
			<key>hash</key>
			<data>
			01mH39v8pvXIdzB3N/3bn0uJwVs=
			</data>
			<key>hash2</key>
			<data>
			b8AjRC9SQ0loXRPVCFStdzsSuMehU9ctYVqyfdSj1gk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/rc5.h</key>
		<dict>
			<key>hash</key>
			<data>
			nVMdNFdbOheiSzNQjJ5v92LvEmI=
			</data>
			<key>hash2</key>
			<data>
			wjiVSh3yP1I2LW5fp43yx6WirW75U25InwojKV76DKs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ripemd.h</key>
		<dict>
			<key>hash</key>
			<data>
			UUNVXGUU1UnsGpXivIzpc/ZyFQs=
			</data>
			<key>hash2</key>
			<data>
			jHS5PBCp6Dq8F87TqAIa91BvOaD62rB7XbLU+uvPaLY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/rsa.h</key>
		<dict>
			<key>hash</key>
			<data>
			BXOED7wh3elZhA9q8KoFrXffhUg=
			</data>
			<key>hash2</key>
			<data>
			UJxNuQgRlc9snl/UaDiQrrOVCfKZehmJ3VpXz0MDm8I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/rsaerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			jrv7NJXCaI3Ppho9VNnyZFn9jxQ=
			</data>
			<key>hash2</key>
			<data>
			4DKpM7GkwT7JAddWGtI6zgGIG2DNzIavDkywoloqMlI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/safestack.h</key>
		<dict>
			<key>hash</key>
			<data>
			Cd9WYSUSt9OnXah9XHF3QevCc+E=
			</data>
			<key>hash2</key>
			<data>
			V2Zdq7N/TwvYU1Odk8ZMuK3zf9lVLcn60hXMj0epkcQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/seed.h</key>
		<dict>
			<key>hash</key>
			<data>
			xIqmrORS9667meS1VHouBNO1FGI=
			</data>
			<key>hash2</key>
			<data>
			a/I3PfsQ3Fy8Ymzy/oa5scgjc9eZvda+E+7a99RUDVU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/sha.h</key>
		<dict>
			<key>hash</key>
			<data>
			lu1HA4odImsyOAN6vcDKaHOxMrc=
			</data>
			<key>hash2</key>
			<data>
			C7Z0VIGsVrZ/RQ0JAz6BO/j2pfICXpDV61OeqxrV4yM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/shim.h</key>
		<dict>
			<key>hash</key>
			<data>
			sMxnt7+rU4Gir29aFtcGAwjCdhs=
			</data>
			<key>hash2</key>
			<data>
			Ra475POOCRP20DOT0uDAvv/CkGhwldG3+hjPVXYZ3Ws=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/sm2.h</key>
		<dict>
			<key>hash</key>
			<data>
			J9BR3kk+bjp4DUXqRy3dTOnScEM=
			</data>
			<key>hash2</key>
			<data>
			K7FK9nwl8cJHxILCnHbOuVWycEoqpMFU3fiLPSY8/04=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/sm3.h</key>
		<dict>
			<key>hash</key>
			<data>
			H8hFoehBM7TTiupNtJpraPdF6TE=
			</data>
			<key>hash2</key>
			<data>
			RDSKyY74qd98l7DUslt8I5iPjP24RzWiakKvFsPoZmU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/sm4.h</key>
		<dict>
			<key>hash</key>
			<data>
			u11NBly6iFRmqITEgK0HhoQVACA=
			</data>
			<key>hash2</key>
			<data>
			qj1uC4OsMfDYcvAZmMd1mc/ElaNU336uYHe5RKe5ja8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/srp.h</key>
		<dict>
			<key>hash</key>
			<data>
			hs0FaZQgqkGx2ATJtkZLR3E7ajQ=
			</data>
			<key>hash2</key>
			<data>
			gqCL+ahm3sG33rZrQHdpDO4PbK+R6wATbF7tTo2UPQY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/srtp.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xiev9lzpqvNNo1eXz+UZl4RZh+0=
			</data>
			<key>hash2</key>
			<data>
			5Q4t1d9qDbIZCRzRxnaKbTGe9khbFuHzYfzkMGeEdiY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ssl.h</key>
		<dict>
			<key>hash</key>
			<data>
			mhalYjN/qeuEF+l6WIVquvXvjOs=
			</data>
			<key>hash2</key>
			<data>
			Z+hVP6OlpyPzsPKff23VH/dbSj1OeZ6x2Y710a9gnlI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ssl2.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZUBF9zzQr/EnSx9hH0JCB0ETP8s=
			</data>
			<key>hash2</key>
			<data>
			f7VXoySIrUSiVCCr/4J5q9C9H0q3aOc9Ph1cLas2wMU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ssl3.h</key>
		<dict>
			<key>hash</key>
			<data>
			cA/eJmiVcOy62x5KksvM5fafytI=
			</data>
			<key>hash2</key>
			<data>
			0Ez+wqn52iqimfVYhCFeIAtJCm4KlCMlUmJki9im0cA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/sslerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			4EiOgpXMaHQuyZqwDCHkMoCYXhw=
			</data>
			<key>hash2</key>
			<data>
			VKPHhOv6tjHSG7m32zzjgWeswCmeiSC1zm5Bmjc2kwo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/stack.h</key>
		<dict>
			<key>hash</key>
			<data>
			HlhyAUEwF7fsEBjOpYfB+ZGw2SU=
			</data>
			<key>hash2</key>
			<data>
			RbqAO74UAH5JS7JLLKlU9jYu9wChBIDvo9LzrLFZ3uw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/store.h</key>
		<dict>
			<key>hash</key>
			<data>
			f3f0erlSosQY7bDqrHLGzKe7YY4=
			</data>
			<key>hash2</key>
			<data>
			EQW60aMJ0xIqLPvcQJijPjPVDIEY5wuzMvO31u8ru1g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/storeerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			kv/pt/4V9S0com6azZDBL4S2ef4=
			</data>
			<key>hash2</key>
			<data>
			BxxmvMA+1cKiTxlk9F6NGmM/PLSxg8cY3tPiUxL4xOA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/symhacks.h</key>
		<dict>
			<key>hash</key>
			<data>
			9immsicIban6k2s+1MF7N3ITwzE=
			</data>
			<key>hash2</key>
			<data>
			DJ4CalkyokMuPN9977eJYQxCcgEOUeX/BHGAnqu3qi0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/tls1.h</key>
		<dict>
			<key>hash</key>
			<data>
			snV9T38ogNzmGbcPr9Rn32lFHOo=
			</data>
			<key>hash2</key>
			<data>
			1BBMqHIDMrmFL0cl1GYKbPd6UrWH59luwmPplsHQri8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ts.h</key>
		<dict>
			<key>hash</key>
			<data>
			iWDV/jVLCzacuMrj1s833l3wAYY=
			</data>
			<key>hash2</key>
			<data>
			m9A568579rbnH8lmfkTgF/wMt8ecAjvhyWWJTmG3kjg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/tserr.h</key>
		<dict>
			<key>hash</key>
			<data>
			PI3IsQNlWi/RwHMUbbL1VJDUvj0=
			</data>
			<key>hash2</key>
			<data>
			tuG7yMU+T3wFR2jexVJy0AHfv+54ioW6iwwGngjLvoU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/txt_db.h</key>
		<dict>
			<key>hash</key>
			<data>
			NoTeslBGWtynVU+GDhyxyzXUdTE=
			</data>
			<key>hash2</key>
			<data>
			kDaWvVuZCFMPioV4/vR3IfR/P+hQe6x3YUc+kl6UIVk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/ui.h</key>
		<dict>
			<key>hash</key>
			<data>
			++9liaOBXfJY40d+p4saEce2zpw=
			</data>
			<key>hash2</key>
			<data>
			9eo/XS576z6B24wjxuYY445RHCE/2ToR71G5rkrQNhk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/uierr.h</key>
		<dict>
			<key>hash</key>
			<data>
			R61EyGTilaauZmnHry4jjFiN5ko=
			</data>
			<key>hash2</key>
			<data>
			uzk3fHAtl2VUfOlfnnWLRqVO5OJon4kgM88qTqV9XQs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/whrlpool.h</key>
		<dict>
			<key>hash</key>
			<data>
			Uo0K/dGVqhsRUor/8CFpmWNaoHY=
			</data>
			<key>hash2</key>
			<data>
			OHUHItFzcIP6jK7fzNO85XQIDjVpIBDS+R3TA7FUr2k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/x509.h</key>
		<dict>
			<key>hash</key>
			<data>
			5wWbXaF8/6/pZR0i8O64cxA2vYY=
			</data>
			<key>hash2</key>
			<data>
			8bizVtZJ2iQjbVqr36/gR7UWsgCNjV0J2l6Z+8S0qYw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/x509_vfy.h</key>
		<dict>
			<key>hash</key>
			<data>
			+5MyYNetMS2YX3CRFqSzMEZc4TE=
			</data>
			<key>hash2</key>
			<data>
			9iGsTDiom9AQZWygCTd3MSan7zSLKaFLBMxexcCVtTE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/x509err.h</key>
		<dict>
			<key>hash</key>
			<data>
			z4/1nUkbgTSuqRKSBA/+tS9zTD4=
			</data>
			<key>hash2</key>
			<data>
			YiSZuaqlRpV8gC2pruNnlMJMnH0uEFrmm0eEHs2sa3o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/x509v3.h</key>
		<dict>
			<key>hash</key>
			<data>
			YhR8NOnXe50FuvmDc73j2oiOZGo=
			</data>
			<key>hash2</key>
			<data>
			p+eKGFhpfnWUC0FroftvThNXIuiMDCgK+Kl5l1xUQ9o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Headers/x509v3err.h</key>
		<dict>
			<key>hash</key>
			<data>
			X1T6LneJ+WEeudGjNgNYP08RqMU=
			</data>
			<key>hash2</key>
			<data>
			PRfcyid81vY3OjCm4H4aLEQCSguYDMzMJTPXi/DCJ3Y=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			or0PHfwEUfPWO1329QfRYfrjYp8=
			</data>
			<key>hash2</key>
			<data>
			sdvtmRhadddbji+XRs/TBCEhV+4iCfCA2mXB4MbcgrM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			wSMjVnQJnXCQkrl0p+m5ijRHe3c=
			</data>
			<key>hash2</key>
			<data>
			Iyk3YE2iKlYEZnIdRRtNf4piXHBV2iLaIdaHtxJFxL8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/OpenSSL</key>
		<dict>
			<key>hash</key>
			<data>
			aVgJxBiHccZVdkegDSyK0AIjciU=
			</data>
			<key>hash2</key>
			<data>
			nrjbg+hFkohvvswvQZBt79j2Us2kCL9gg/3b2g+nenI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			AL1dh5ctObXBjoBiabSJ86M3HQs=
			</data>
			<key>hash2</key>
			<data>
			WpuPwM3bECAbtHzCgEs/AExyUUdmItJb/E61TtRuEIQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash</key>
			<data>
			nZrgLKdcnfl/v6/JXfo3UKn0X8A=
			</data>
			<key>hash2</key>
			<data>
			FOSRN635YChoVY2e/GuvzGLnNbtqJY3N5JOlf6h3cIc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash</key>
			<data>
			OnX22wWFKRSOFN1+obRynMCeyXM=
			</data>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash</key>
			<data>
			J4qTysq8UHbs7drizZAHzRAr2uo=
			</data>
			<key>hash2</key>
			<data>
			KmAO9QRZnaDWhOJnZt/+YcH7frAB/BWzOYJ7GQt7IK4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			syqOt6hQ6uT0w4z/YhfxE1+8OK0=
			</data>
			<key>hash2</key>
			<data>
			FlaCk4FgjbypDAdtLRNQQukF+GsJ3dKdry+tSzaDBu8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/OpenSSL.framework/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash</key>
			<data>
			2jmj7l5rSw0yVb/vlWAYkK/YBwk=
			</data>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Headers</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Headers</string>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/OpenSSL</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/OpenSSL</string>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/OpenSSL.h</key>
		<dict>
			<key>hash</key>
			<data>
			dH3RGwhv9Otw3o0azCaKlGRCtgQ=
			</data>
			<key>hash2</key>
			<data>
			oD4AN3Jc49L+onZIG31G21nR9xRUFrWNbAunP8zb9sE=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/aes.h</key>
		<dict>
			<key>hash</key>
			<data>
			/QHXsfp5KZBtt0hpQ+PGhRB5TQE=
			</data>
			<key>hash2</key>
			<data>
			9ron6H2X6lePAVQKj4S36rBdQsF4utznEtDMZo2KmYE=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/asn1.h</key>
		<dict>
			<key>hash</key>
			<data>
			/JULnimbzTltoXNqIFMZvE15UFw=
			</data>
			<key>hash2</key>
			<data>
			hRMTsRdrqj1olqru6+9WIW69043Nq7ObPso2tmRTUrw=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/asn1err.h</key>
		<dict>
			<key>hash</key>
			<data>
			vClK4nFx8VUb19HBjU1cQuNfemY=
			</data>
			<key>hash2</key>
			<data>
			uJsWIW3dNfAo9b/T/g208urWyUFed0m3EVsDk7Qn7y8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/asn1t.h</key>
		<dict>
			<key>hash</key>
			<data>
			LWrSPPRO1SZ6tiCyd9G2baHBT0g=
			</data>
			<key>hash2</key>
			<data>
			JwL1aTAiSLgijkkxd88KRp8Sfxzlx2vGhLyQ+9/EV0M=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/async.h</key>
		<dict>
			<key>hash</key>
			<data>
			Du5KwAWX1O4Ld/G5CC+CgQkQU9M=
			</data>
			<key>hash2</key>
			<data>
			75bHFsrDk/LNbaMErwKRVezZ8CGGnNWkw0Hr3ySQNrw=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/asyncerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			K5Jc+TuPuL/d4mP5tAhHKE2LRmc=
			</data>
			<key>hash2</key>
			<data>
			ysycqVGqUj0+1rnfI2YCJjOSWmcp0wzRNKBdL/5fsVY=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/bio.h</key>
		<dict>
			<key>hash</key>
			<data>
			3e8z1vvxgbXPwmeg+1bRSmgfCG8=
			</data>
			<key>hash2</key>
			<data>
			eLBVVVlX7sK/tM5fkbJ73P33MSgCa+nOhfzKASb9zT0=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/bioerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			N0rVDI9qQwyDEWvlqW1vGQrh0RU=
			</data>
			<key>hash2</key>
			<data>
			rSn+TvN/18SyVraQyvk3GnTimYH2VhuUQ22sVKYb90s=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/blowfish.h</key>
		<dict>
			<key>hash</key>
			<data>
			BLqJpLWCl4Gl0DR4WO0luoyixMg=
			</data>
			<key>hash2</key>
			<data>
			gTssfP+Ii1twm5DU2lhK/vWbXOHqiIYX1oLxr0hXj68=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/bn.h</key>
		<dict>
			<key>hash</key>
			<data>
			zwKCI0fcYjm7MT9ZsUzGG4BDLTE=
			</data>
			<key>hash2</key>
			<data>
			j3mynFrUeRhqyq6WKlmNmGjcQ0/V+kmgCO7Pr/G5mMo=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/bnerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			ybn9OmsYdCvnSKGlXSQQJ8b+Y44=
			</data>
			<key>hash2</key>
			<data>
			gJirlUUwfW4snMKK6ZXW/aDolZthZ8wD1ncWsCq7J/8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/buffer.h</key>
		<dict>
			<key>hash</key>
			<data>
			70BHajzy0JGUYwo7F+dpD0CeWrs=
			</data>
			<key>hash2</key>
			<data>
			pPopK5A8uMLOF2G6SYDPW9+2bcrcu1yK7MRbASvGTCM=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/buffererr.h</key>
		<dict>
			<key>hash</key>
			<data>
			PSVdu9joU+POeEegSPtAPctXWyI=
			</data>
			<key>hash2</key>
			<data>
			ci9uqHcRt8DLbuKcFXYsKDnlrRe571ebboG2Eh8DXDA=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/camellia.h</key>
		<dict>
			<key>hash</key>
			<data>
			R0cxfQe4VMejfw/FBnV5jlrTxS8=
			</data>
			<key>hash2</key>
			<data>
			VhvbLphUWOgJ5lbGDkvIPh1nB3Rt2LW628ELZBmNR3A=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/cast.h</key>
		<dict>
			<key>hash</key>
			<data>
			tg9fweKyld2MF5c1iw7sEh5btDM=
			</data>
			<key>hash2</key>
			<data>
			oUxRoSmzrleV2v2YBlvguwy/ALTUUo6Frb9imWb0L1M=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/cmac.h</key>
		<dict>
			<key>hash</key>
			<data>
			SsfJcPvnO3RZ7i+QyWeuyIBoFr4=
			</data>
			<key>hash2</key>
			<data>
			db+V0doRj/Hi8UPMt6Vk2UneRACXuXpJclwDl24We1I=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/cms.h</key>
		<dict>
			<key>hash</key>
			<data>
			WhfyS38eVlM/0EjH+SRNVL1fhB0=
			</data>
			<key>hash2</key>
			<data>
			B+Swp3nJV++QJtacDLWmId3CBgr5Z+DdXpGshh/fvbI=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/cmserr.h</key>
		<dict>
			<key>hash</key>
			<data>
			icv8DehBtI8fn3XrZvJ8GaK3Nm8=
			</data>
			<key>hash2</key>
			<data>
			fg/dq6hfs9ESYq7ixgJyWS3uGmI0pSqUsGuv0cClQ1A=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/comp.h</key>
		<dict>
			<key>hash</key>
			<data>
			iXVcuTWmFCf9QLEmp9sNP8JQUw0=
			</data>
			<key>hash2</key>
			<data>
			mKh3xicj5gQuTkdAzRpud5nfdS2SNDhtc6KKCY5usxw=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/comperr.h</key>
		<dict>
			<key>hash</key>
			<data>
			smXqglFOEgRt0CXetpBcXnWbZYI=
			</data>
			<key>hash2</key>
			<data>
			9kviBdCK8lVxh+wZwDZ4qp4p9m5CjeKc691izXw7VRU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/conf.h</key>
		<dict>
			<key>hash</key>
			<data>
			lY422quRPGGoMtvoZu73RVfY38s=
			</data>
			<key>hash2</key>
			<data>
			5UNB4wUj2twJ2CE2b1r7E8kobVQLNMGUVAaRjtWI+iE=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/conf_api.h</key>
		<dict>
			<key>hash</key>
			<data>
			aEkI7MCNJGZ+SJxs514uMY1oW3s=
			</data>
			<key>hash2</key>
			<data>
			F1+GtptYsM0dpsmyfYerTk7VLzfcdreJXCEMQ0aVBOs=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/conferr.h</key>
		<dict>
			<key>hash</key>
			<data>
			HhxJvI2jMpKEWKwpMC+m/3YD5tE=
			</data>
			<key>hash2</key>
			<data>
			q/4mSFzUOoDgxHbmKJeWEq0opjVXe69o7uR2hQQlpJg=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/crypto.h</key>
		<dict>
			<key>hash</key>
			<data>
			9mlWjKYcqA63rFf05YOE/OnlLlE=
			</data>
			<key>hash2</key>
			<data>
			0s2E5pXmgFH+Z/p8+QHMuk+tzMcaazAJDfIKAbkdPsc=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/cryptoerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			rvUn24W+SZM/y6GTW6NG1DW8KQo=
			</data>
			<key>hash2</key>
			<data>
			51LX09oypsAJzyZEUHJjZ/1p58KkwYXVgM5lACHWjno=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ct.h</key>
		<dict>
			<key>hash</key>
			<data>
			cpeLfPNApLUZ414BRmIcahaW0y0=
			</data>
			<key>hash2</key>
			<data>
			snqvDTlglQC0/OOztl9nUhFqyzBCDeHVOUMHSkgQQ/A=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/cterr.h</key>
		<dict>
			<key>hash</key>
			<data>
			z6hnmIDS0tby0u7XWscijJOnipQ=
			</data>
			<key>hash2</key>
			<data>
			q25wtS40EkfEsheIOYD/HpjkRT0QILCwOM2/CWxKuKU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/des.h</key>
		<dict>
			<key>hash</key>
			<data>
			znOw/0Vq2B1QWQ5QlySAEIkrdwE=
			</data>
			<key>hash2</key>
			<data>
			PLaPu0mONOGeRvnUFWFZaracz3ix2L3y8xLoksBgAV8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/dh.h</key>
		<dict>
			<key>hash</key>
			<data>
			9zO15LRHFl5v1MTrCuS3Q2iGGu0=
			</data>
			<key>hash2</key>
			<data>
			RtDEiea4BjeSz52Bk/UnOPDY/spOuERV4KNZY21BqeA=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/dherr.h</key>
		<dict>
			<key>hash</key>
			<data>
			PTwLUzp4LhfTwc/VMpRyT9YTuRU=
			</data>
			<key>hash2</key>
			<data>
			cpkNxez4V74yHIi5IVAshTZX+AnsU+r5GgEcCZqFdw0=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/dsa.h</key>
		<dict>
			<key>hash</key>
			<data>
			AEYyYVu2fy7yPpiyRyjyypHNCKI=
			</data>
			<key>hash2</key>
			<data>
			FSXlrfJCZDp9NjElgDFPXOYeyt+CIjpJ/1wyIg14+bk=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/dsaerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			jmOqsG1TSs1yTIm74Qstlz/KODg=
			</data>
			<key>hash2</key>
			<data>
			ZeqLR8slroLWETN81H2nwcXNuUUZgr7snYQUE9SpI6k=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/dtls1.h</key>
		<dict>
			<key>hash</key>
			<data>
			fDDC+ixg7o1tDB4basD847J6+Zk=
			</data>
			<key>hash2</key>
			<data>
			cwTxfMmZi9fBZTmuml6lRfLgxkCG1eaIyroQS2p//sw=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/e_os2.h</key>
		<dict>
			<key>hash</key>
			<data>
			lavqS62GkOuhGihyu+WOjVd92SM=
			</data>
			<key>hash2</key>
			<data>
			DWGWKc2z+15UMR6KP/YC+DjEpFMKVr4/0KgefosQy4Q=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ebcdic.h</key>
		<dict>
			<key>hash</key>
			<data>
			z5Fn9TbPaQo8zoY+Uwo/lSr9SJ8=
			</data>
			<key>hash2</key>
			<data>
			IonxTxHnWsc50RI2UdFuhB88dONtqkg8H+2fjFwURyA=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ec.h</key>
		<dict>
			<key>hash</key>
			<data>
			VDanIs80uH9tNKL+PSMmYL0YIu4=
			</data>
			<key>hash2</key>
			<data>
			TmJAZFMZhlYHifhQnf0QvF7YLrZVmeA+07/tV75t8Zk=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ecdh.h</key>
		<dict>
			<key>hash</key>
			<data>
			iXUqwjlcXCix2i1+T/zXRV4PU18=
			</data>
			<key>hash2</key>
			<data>
			lfuJrdO9MrbUPc8aUdGDm5Fed01xOK+vYY5pDvv0FLs=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ecdsa.h</key>
		<dict>
			<key>hash</key>
			<data>
			iXUqwjlcXCix2i1+T/zXRV4PU18=
			</data>
			<key>hash2</key>
			<data>
			lfuJrdO9MrbUPc8aUdGDm5Fed01xOK+vYY5pDvv0FLs=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ecerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			GyM9ZofjjCqJIe+IbO9j4p+HDZk=
			</data>
			<key>hash2</key>
			<data>
			haO2TAZEQgoYKVyAAaBrrV18PoTU+HHJJaG0X7ie8YI=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/engine.h</key>
		<dict>
			<key>hash</key>
			<data>
			dS4eXHBYjCoSL3nDXCYuyZR9V9c=
			</data>
			<key>hash2</key>
			<data>
			qY3weurFeSk7GG6mHDXtzj7r3h/QPYVTVj8YAj3kfJU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/engineerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			H6oGO47GeHqzTYa913WPlF5cZzQ=
			</data>
			<key>hash2</key>
			<data>
			bz0ekbobeXGSlTDLisPvk0wLgqjNRZyvIk4kGj5a1uk=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/err.h</key>
		<dict>
			<key>hash</key>
			<data>
			OZsmLZ7UryVhV7QY17J5cymnWlY=
			</data>
			<key>hash2</key>
			<data>
			FEcmPwqEDoE1xYbhbD2FjO6Tne3fT9kF45GGmAm02qI=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/evp.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ue0l0jetFcNRP+2k4ve5l9kWsOw=
			</data>
			<key>hash2</key>
			<data>
			nZY73agYApiBcGY8z870N66mkEL0Ku86EatKIXuACu8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/evperr.h</key>
		<dict>
			<key>hash</key>
			<data>
			YPfW2qvVYScvnOl+vKfUlzf4pvg=
			</data>
			<key>hash2</key>
			<data>
			RdaLRi8lI5SIwCC+5fv2lwCwzXtNwrO6cBW93EmUdqQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/hmac.h</key>
		<dict>
			<key>hash</key>
			<data>
			oNXOm72CsIiR3i2/7dzmPfXHpnk=
			</data>
			<key>hash2</key>
			<data>
			djhsInPxBfVMV0ny/IVFc8NxqxhfkkjOKVp0jhJsqug=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/idea.h</key>
		<dict>
			<key>hash</key>
			<data>
			9OhfGjNERiWm+IaFZng3mj74a70=
			</data>
			<key>hash2</key>
			<data>
			uraCxcwGAnyC0SbtjmXX3Z3MdfpGTMvJXX8Wi9accy0=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/kdf.h</key>
		<dict>
			<key>hash</key>
			<data>
			8bAA2Z5x72kQuKB9lY8B07YpOvA=
			</data>
			<key>hash2</key>
			<data>
			qq/UqWK6m58qJGIHbH97SZHZ2H5773HYfpMIEl4OKcA=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/kdferr.h</key>
		<dict>
			<key>hash</key>
			<data>
			eBl7XkjAByV3fh1j+2ZSsSVdPd0=
			</data>
			<key>hash2</key>
			<data>
			5AbLyKinlWvLDgapSUVLSNJwIznK9KLmIawM3pwkY+M=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/lhash.h</key>
		<dict>
			<key>hash</key>
			<data>
			gsp3p9sYfgyLm8w777aVi7LZzaA=
			</data>
			<key>hash2</key>
			<data>
			EDbiCroA4FhblrkaAK53ktwSUBFg4RfR+CSDPef+N1I=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/md2.h</key>
		<dict>
			<key>hash</key>
			<data>
			SU5g+hFH8KXJwSElUE6qnz88XbQ=
			</data>
			<key>hash2</key>
			<data>
			nijwqNXiKXZJrymPXjIJ0y/pSG7bF5PMaYp1fJEnBmI=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/md4.h</key>
		<dict>
			<key>hash</key>
			<data>
			NVmYVdXaFSHylpRJRh52LUqSAIY=
			</data>
			<key>hash2</key>
			<data>
			A9P8nc7sbBaNIZ659ATKCLR41VIWIoNLwkt9RZRXAuQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/md5.h</key>
		<dict>
			<key>hash</key>
			<data>
			8R2dids4HGec0BuJ5RjnI0sNAqs=
			</data>
			<key>hash2</key>
			<data>
			oGEqj35p47wWb8GGykS+HlsdAgtdqLW+Ah9z1IwnC4I=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/mdc2.h</key>
		<dict>
			<key>hash</key>
			<data>
			ceP5kO5gOJDJGS7HrDRjpWWG2i4=
			</data>
			<key>hash2</key>
			<data>
			8TsB7AnEXCY0Zz0bmxp5rb1uwyvf+UKHMI4rsnQI5Tc=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/modes.h</key>
		<dict>
			<key>hash</key>
			<data>
			oykGDYKdMkpM+dh+C5148xyIG9A=
			</data>
			<key>hash2</key>
			<data>
			9OUnmH4paib8HAb0+Ja6pfRXt+Z/lJV9F27tRpsP5gI=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/obj_mac.h</key>
		<dict>
			<key>hash</key>
			<data>
			0xczM7tqfkjs0KVU5TgD3MmB0O8=
			</data>
			<key>hash2</key>
			<data>
			gevKIMw3DM9y8xA9EjHGpC4GRzC7IiYal9ht3Q57wIo=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/objects.h</key>
		<dict>
			<key>hash</key>
			<data>
			1BKkzZQ86VAQYSD2wS3W+kAe8lU=
			</data>
			<key>hash2</key>
			<data>
			QBjXxU5vGcMjCvBj9NvIUj8UUFKKSvFal6Ch/z+zujg=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/objectserr.h</key>
		<dict>
			<key>hash</key>
			<data>
			62CLZz09xeqGq4nUpt1HfuvRrbM=
			</data>
			<key>hash2</key>
			<data>
			DsczDBIq4rMXTfld3s6mX/ZhphUs0BklKd0UEe8/YqE=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ocsp.h</key>
		<dict>
			<key>hash</key>
			<data>
			/vPykCbZnxZ/oD4x73y3vCFlfAg=
			</data>
			<key>hash2</key>
			<data>
			z7vDQ0tWxGcQgDJq64w9AH2rfSw2IdUUbBGro0cVi4A=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ocsperr.h</key>
		<dict>
			<key>hash</key>
			<data>
			HzVTI0XxPNFGeTAYcha32Bf1qLw=
			</data>
			<key>hash2</key>
			<data>
			xzpXsZGWAf+MBPLcnGLb0TDvLTrjm7D83yW8nm6vMno=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/opensslconf.h</key>
		<dict>
			<key>hash</key>
			<data>
			yNGszHsg1rO96Mwyt+XRNUDMH3E=
			</data>
			<key>hash2</key>
			<data>
			TobY/gSgSFAftkR9MCoN++X6EOEwraLOKY4xULr1+Zo=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/opensslv.h</key>
		<dict>
			<key>hash</key>
			<data>
			RV0inZj+UfBzOwFBUF97hvsSAjg=
			</data>
			<key>hash2</key>
			<data>
			DKEsKrqL++JMTAaGHp1XuTnF5y0cCPN5ikQtaY3rdZk=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ossl_typ.h</key>
		<dict>
			<key>hash</key>
			<data>
			/hD+IfNYoPQpFE2wIl8jHi6BRVI=
			</data>
			<key>hash2</key>
			<data>
			RvVBocxJvwiQirO94F4mg/xW6iaMo+fy4nYoZT7LShQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/pem.h</key>
		<dict>
			<key>hash</key>
			<data>
			32ly0pNLPzdTYGECzCbGY6huGuw=
			</data>
			<key>hash2</key>
			<data>
			MtYgNtDDXQP+f33ar/48rq/PmE7Bbk237RnT4gLp17o=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/pem2.h</key>
		<dict>
			<key>hash</key>
			<data>
			t2jj9ipWiYEJZvgm7/w68FCv7xk=
			</data>
			<key>hash2</key>
			<data>
			aDPuVxISXRzo97UkN6dS5AwvR5MnaFmoIowN5x01w+A=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/pemerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			LPK/t/8oy0h9S1TUlxQnvG640ps=
			</data>
			<key>hash2</key>
			<data>
			2RxgFvPBunBoO5L95VUA7DNHd977ZIbEGwqcTkAqopU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/pkcs12.h</key>
		<dict>
			<key>hash</key>
			<data>
			Wgtt5Ajf2I0Qe0Yw/C6/da673Vs=
			</data>
			<key>hash2</key>
			<data>
			l8Epxsmkk9fl068SPZYEDYfE5U/F5BrsRQgyyzKmNNY=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/pkcs12err.h</key>
		<dict>
			<key>hash</key>
			<data>
			iYGY/k8EiROz+H6/d3nIzlOnXHY=
			</data>
			<key>hash2</key>
			<data>
			tzQHJ6BIDzUYI7Zp9VzTmJwGXTc8jPb/h0W4NW9h1f8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/pkcs7.h</key>
		<dict>
			<key>hash</key>
			<data>
			DSVKNOqJWVpNEj9g1afFP/AAHbs=
			</data>
			<key>hash2</key>
			<data>
			zcNQXJu5FopvrUNN2dmkmtYwua6SFr1mWxEFHgSnCbw=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/pkcs7err.h</key>
		<dict>
			<key>hash</key>
			<data>
			vbVkdgXZDNlmUBjHsDcvjoyWNTE=
			</data>
			<key>hash2</key>
			<data>
			7O4R6fvdqyD3igGACeai2vKH/13wBnkpixN/6Zltk4Y=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/rand.h</key>
		<dict>
			<key>hash</key>
			<data>
			BUI/kpuBobNUPlXrW/OkHeq9ClM=
			</data>
			<key>hash2</key>
			<data>
			WpGry90U3PL8YnYczFjulAcnTqVYwpL6d61iUgSsK9c=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/rand_drbg.h</key>
		<dict>
			<key>hash</key>
			<data>
			dZJZsnraib5Oxt12qSJ4bXxP3/w=
			</data>
			<key>hash2</key>
			<data>
			EjecwKfxaMv/jgiCjactwOh3c7xsO9FMS1dQYzm2H6U=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/randerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			/b3o6sjMLxkewJmLW7LeYF/10A0=
			</data>
			<key>hash2</key>
			<data>
			Qfzr+wdnr6A/PeJzLT8M5GzSNn7DSyAWzbXJ6EqaiZQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/rc2.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xzwvx1iv4W35klxWCpyRR35/Uwc=
			</data>
			<key>hash2</key>
			<data>
			7CuRlomLvEX/KrACBPk6byDJdCJVEPKQl61ppu7rzf4=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/rc4.h</key>
		<dict>
			<key>hash</key>
			<data>
			01mH39v8pvXIdzB3N/3bn0uJwVs=
			</data>
			<key>hash2</key>
			<data>
			b8AjRC9SQ0loXRPVCFStdzsSuMehU9ctYVqyfdSj1gk=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/rc5.h</key>
		<dict>
			<key>hash</key>
			<data>
			nVMdNFdbOheiSzNQjJ5v92LvEmI=
			</data>
			<key>hash2</key>
			<data>
			wjiVSh3yP1I2LW5fp43yx6WirW75U25InwojKV76DKs=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ripemd.h</key>
		<dict>
			<key>hash</key>
			<data>
			UUNVXGUU1UnsGpXivIzpc/ZyFQs=
			</data>
			<key>hash2</key>
			<data>
			jHS5PBCp6Dq8F87TqAIa91BvOaD62rB7XbLU+uvPaLY=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/rsa.h</key>
		<dict>
			<key>hash</key>
			<data>
			BXOED7wh3elZhA9q8KoFrXffhUg=
			</data>
			<key>hash2</key>
			<data>
			UJxNuQgRlc9snl/UaDiQrrOVCfKZehmJ3VpXz0MDm8I=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/rsaerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			jrv7NJXCaI3Ppho9VNnyZFn9jxQ=
			</data>
			<key>hash2</key>
			<data>
			4DKpM7GkwT7JAddWGtI6zgGIG2DNzIavDkywoloqMlI=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/safestack.h</key>
		<dict>
			<key>hash</key>
			<data>
			Cd9WYSUSt9OnXah9XHF3QevCc+E=
			</data>
			<key>hash2</key>
			<data>
			V2Zdq7N/TwvYU1Odk8ZMuK3zf9lVLcn60hXMj0epkcQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/seed.h</key>
		<dict>
			<key>hash</key>
			<data>
			xIqmrORS9667meS1VHouBNO1FGI=
			</data>
			<key>hash2</key>
			<data>
			a/I3PfsQ3Fy8Ymzy/oa5scgjc9eZvda+E+7a99RUDVU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/sha.h</key>
		<dict>
			<key>hash</key>
			<data>
			lu1HA4odImsyOAN6vcDKaHOxMrc=
			</data>
			<key>hash2</key>
			<data>
			C7Z0VIGsVrZ/RQ0JAz6BO/j2pfICXpDV61OeqxrV4yM=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/shim.h</key>
		<dict>
			<key>hash</key>
			<data>
			sMxnt7+rU4Gir29aFtcGAwjCdhs=
			</data>
			<key>hash2</key>
			<data>
			Ra475POOCRP20DOT0uDAvv/CkGhwldG3+hjPVXYZ3Ws=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/sm2.h</key>
		<dict>
			<key>hash</key>
			<data>
			J9BR3kk+bjp4DUXqRy3dTOnScEM=
			</data>
			<key>hash2</key>
			<data>
			K7FK9nwl8cJHxILCnHbOuVWycEoqpMFU3fiLPSY8/04=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/sm3.h</key>
		<dict>
			<key>hash</key>
			<data>
			H8hFoehBM7TTiupNtJpraPdF6TE=
			</data>
			<key>hash2</key>
			<data>
			RDSKyY74qd98l7DUslt8I5iPjP24RzWiakKvFsPoZmU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/sm4.h</key>
		<dict>
			<key>hash</key>
			<data>
			u11NBly6iFRmqITEgK0HhoQVACA=
			</data>
			<key>hash2</key>
			<data>
			qj1uC4OsMfDYcvAZmMd1mc/ElaNU336uYHe5RKe5ja8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/srp.h</key>
		<dict>
			<key>hash</key>
			<data>
			hs0FaZQgqkGx2ATJtkZLR3E7ajQ=
			</data>
			<key>hash2</key>
			<data>
			gqCL+ahm3sG33rZrQHdpDO4PbK+R6wATbF7tTo2UPQY=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/srtp.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xiev9lzpqvNNo1eXz+UZl4RZh+0=
			</data>
			<key>hash2</key>
			<data>
			5Q4t1d9qDbIZCRzRxnaKbTGe9khbFuHzYfzkMGeEdiY=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ssl.h</key>
		<dict>
			<key>hash</key>
			<data>
			mhalYjN/qeuEF+l6WIVquvXvjOs=
			</data>
			<key>hash2</key>
			<data>
			Z+hVP6OlpyPzsPKff23VH/dbSj1OeZ6x2Y710a9gnlI=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ssl2.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZUBF9zzQr/EnSx9hH0JCB0ETP8s=
			</data>
			<key>hash2</key>
			<data>
			f7VXoySIrUSiVCCr/4J5q9C9H0q3aOc9Ph1cLas2wMU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ssl3.h</key>
		<dict>
			<key>hash</key>
			<data>
			cA/eJmiVcOy62x5KksvM5fafytI=
			</data>
			<key>hash2</key>
			<data>
			0Ez+wqn52iqimfVYhCFeIAtJCm4KlCMlUmJki9im0cA=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/sslerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			4EiOgpXMaHQuyZqwDCHkMoCYXhw=
			</data>
			<key>hash2</key>
			<data>
			VKPHhOv6tjHSG7m32zzjgWeswCmeiSC1zm5Bmjc2kwo=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/stack.h</key>
		<dict>
			<key>hash</key>
			<data>
			HlhyAUEwF7fsEBjOpYfB+ZGw2SU=
			</data>
			<key>hash2</key>
			<data>
			RbqAO74UAH5JS7JLLKlU9jYu9wChBIDvo9LzrLFZ3uw=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/store.h</key>
		<dict>
			<key>hash</key>
			<data>
			f3f0erlSosQY7bDqrHLGzKe7YY4=
			</data>
			<key>hash2</key>
			<data>
			EQW60aMJ0xIqLPvcQJijPjPVDIEY5wuzMvO31u8ru1g=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/storeerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			kv/pt/4V9S0com6azZDBL4S2ef4=
			</data>
			<key>hash2</key>
			<data>
			BxxmvMA+1cKiTxlk9F6NGmM/PLSxg8cY3tPiUxL4xOA=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/symhacks.h</key>
		<dict>
			<key>hash</key>
			<data>
			9immsicIban6k2s+1MF7N3ITwzE=
			</data>
			<key>hash2</key>
			<data>
			DJ4CalkyokMuPN9977eJYQxCcgEOUeX/BHGAnqu3qi0=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/tls1.h</key>
		<dict>
			<key>hash</key>
			<data>
			snV9T38ogNzmGbcPr9Rn32lFHOo=
			</data>
			<key>hash2</key>
			<data>
			1BBMqHIDMrmFL0cl1GYKbPd6UrWH59luwmPplsHQri8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ts.h</key>
		<dict>
			<key>hash</key>
			<data>
			iWDV/jVLCzacuMrj1s833l3wAYY=
			</data>
			<key>hash2</key>
			<data>
			m9A568579rbnH8lmfkTgF/wMt8ecAjvhyWWJTmG3kjg=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/tserr.h</key>
		<dict>
			<key>hash</key>
			<data>
			PI3IsQNlWi/RwHMUbbL1VJDUvj0=
			</data>
			<key>hash2</key>
			<data>
			tuG7yMU+T3wFR2jexVJy0AHfv+54ioW6iwwGngjLvoU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/txt_db.h</key>
		<dict>
			<key>hash</key>
			<data>
			NoTeslBGWtynVU+GDhyxyzXUdTE=
			</data>
			<key>hash2</key>
			<data>
			kDaWvVuZCFMPioV4/vR3IfR/P+hQe6x3YUc+kl6UIVk=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/ui.h</key>
		<dict>
			<key>hash</key>
			<data>
			++9liaOBXfJY40d+p4saEce2zpw=
			</data>
			<key>hash2</key>
			<data>
			9eo/XS576z6B24wjxuYY445RHCE/2ToR71G5rkrQNhk=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/uierr.h</key>
		<dict>
			<key>hash</key>
			<data>
			R61EyGTilaauZmnHry4jjFiN5ko=
			</data>
			<key>hash2</key>
			<data>
			uzk3fHAtl2VUfOlfnnWLRqVO5OJon4kgM88qTqV9XQs=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/whrlpool.h</key>
		<dict>
			<key>hash</key>
			<data>
			Uo0K/dGVqhsRUor/8CFpmWNaoHY=
			</data>
			<key>hash2</key>
			<data>
			OHUHItFzcIP6jK7fzNO85XQIDjVpIBDS+R3TA7FUr2k=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/x509.h</key>
		<dict>
			<key>hash</key>
			<data>
			5wWbXaF8/6/pZR0i8O64cxA2vYY=
			</data>
			<key>hash2</key>
			<data>
			8bizVtZJ2iQjbVqr36/gR7UWsgCNjV0J2l6Z+8S0qYw=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/x509_vfy.h</key>
		<dict>
			<key>hash</key>
			<data>
			+5MyYNetMS2YX3CRFqSzMEZc4TE=
			</data>
			<key>hash2</key>
			<data>
			9iGsTDiom9AQZWygCTd3MSan7zSLKaFLBMxexcCVtTE=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/x509err.h</key>
		<dict>
			<key>hash</key>
			<data>
			z4/1nUkbgTSuqRKSBA/+tS9zTD4=
			</data>
			<key>hash2</key>
			<data>
			YiSZuaqlRpV8gC2pruNnlMJMnH0uEFrmm0eEHs2sa3o=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/x509v3.h</key>
		<dict>
			<key>hash</key>
			<data>
			YhR8NOnXe50FuvmDc73j2oiOZGo=
			</data>
			<key>hash2</key>
			<data>
			p+eKGFhpfnWUC0FroftvThNXIuiMDCgK+Kl5l1xUQ9o=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Headers/x509v3err.h</key>
		<dict>
			<key>hash</key>
			<data>
			X1T6LneJ+WEeudGjNgNYP08RqMU=
			</data>
			<key>hash2</key>
			<data>
			PRfcyid81vY3OjCm4H4aLEQCSguYDMzMJTPXi/DCJ3Y=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			wSMjVnQJnXCQkrl0p+m5ijRHe3c=
			</data>
			<key>hash2</key>
			<data>
			Iyk3YE2iKlYEZnIdRRtNf4piXHBV2iLaIdaHtxJFxL8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/OpenSSL</key>
		<dict>
			<key>hash</key>
			<data>
			LqY/lhq9wJw+eouFUuS8/mp8QKo=
			</data>
			<key>hash2</key>
			<data>
			z9QNd4cr0yJxCWbiMeb+GEdcTG3rfVBHwOHz2qd4qQ4=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			gyD3g6vYmIQTZWlTzAiL16f0sAs=
			</data>
			<key>hash2</key>
			<data>
			GNE6hZnY2rOyEomQAkzfXjz/sqCLiQ3u9KQPCjciQV8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			AL1dh5ctObXBjoBiabSJ86M3HQs=
			</data>
			<key>hash2</key>
			<data>
			WpuPwM3bECAbtHzCgEs/AExyUUdmItJb/E61TtRuEIQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash</key>
			<data>
			WYWAuL5uXgb1hrGtV5jcrQ6Wbpc=
			</data>
			<key>hash2</key>
			<data>
			9bxjM3vhwALos4uKEsMWhirr1Hv4js/zgKbwvP5H2e4=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash</key>
			<data>
			LivNFy9yoqb1NZc0y0RjgYMqdYM=
			</data>
			<key>hash2</key>
			<data>
			sJBTjtPXegOlCm3Ps2Nhxa7ulbabs6YEjzSXcxoDZ1o=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash</key>
			<data>
			FVWme/rGk9UIXXP7Xj+3u/yI1q8=
			</data>
			<key>hash2</key>
			<data>
			QgkBK/01wDKmhW6R5CZXMLqvlGKFQEY8smsSYgGrTyE=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			B9AkY04jmtO5OEKNiJvtpb8FdHE=
			</data>
			<key>hash2</key>
			<data>
			tpHE8KR3dAt42wFpOPIW7mOYysneGU3Wk7rqFStSAEA=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/A/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash</key>
			<data>
			jfkdt/uE2KQtM0Ug6YyjoPNtNII=
			</data>
			<key>hash2</key>
			<data>
			74qfwb0/2b+cwWN45PwwTcWJJG5EcP4/1a9xzoi4Hu4=
			</data>
		</dict>
		<key>macos-arm64_x86_64/OpenSSL.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
