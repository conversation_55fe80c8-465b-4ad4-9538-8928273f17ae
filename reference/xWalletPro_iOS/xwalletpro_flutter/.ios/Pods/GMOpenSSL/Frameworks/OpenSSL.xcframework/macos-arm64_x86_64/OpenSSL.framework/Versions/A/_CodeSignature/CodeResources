<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		gyD3g6vYmIQTZWlTzAiL16f0sAs=
		</data>
		<key>Resources/PrivacyInfo.xcprivacy</key>
		<data>
		AL1dh5ctObXBjoBiabSJ86M3HQs=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/OpenSSL.h</key>
		<dict>
			<key>hash</key>
			<data>
			dH3RGwhv9Otw3o0azCaKlGRCtgQ=
			</data>
			<key>hash2</key>
			<data>
			oD4AN3Jc49L+onZIG31G21nR9xRUFrWNbAunP8zb9sE=
			</data>
		</dict>
		<key>Headers/aes.h</key>
		<dict>
			<key>hash</key>
			<data>
			/QHXsfp5KZBtt0hpQ+PGhRB5TQE=
			</data>
			<key>hash2</key>
			<data>
			9ron6H2X6lePAVQKj4S36rBdQsF4utznEtDMZo2KmYE=
			</data>
		</dict>
		<key>Headers/asn1.h</key>
		<dict>
			<key>hash</key>
			<data>
			/JULnimbzTltoXNqIFMZvE15UFw=
			</data>
			<key>hash2</key>
			<data>
			hRMTsRdrqj1olqru6+9WIW69043Nq7ObPso2tmRTUrw=
			</data>
		</dict>
		<key>Headers/asn1err.h</key>
		<dict>
			<key>hash</key>
			<data>
			vClK4nFx8VUb19HBjU1cQuNfemY=
			</data>
			<key>hash2</key>
			<data>
			uJsWIW3dNfAo9b/T/g208urWyUFed0m3EVsDk7Qn7y8=
			</data>
		</dict>
		<key>Headers/asn1t.h</key>
		<dict>
			<key>hash</key>
			<data>
			LWrSPPRO1SZ6tiCyd9G2baHBT0g=
			</data>
			<key>hash2</key>
			<data>
			JwL1aTAiSLgijkkxd88KRp8Sfxzlx2vGhLyQ+9/EV0M=
			</data>
		</dict>
		<key>Headers/async.h</key>
		<dict>
			<key>hash</key>
			<data>
			Du5KwAWX1O4Ld/G5CC+CgQkQU9M=
			</data>
			<key>hash2</key>
			<data>
			75bHFsrDk/LNbaMErwKRVezZ8CGGnNWkw0Hr3ySQNrw=
			</data>
		</dict>
		<key>Headers/asyncerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			K5Jc+TuPuL/d4mP5tAhHKE2LRmc=
			</data>
			<key>hash2</key>
			<data>
			ysycqVGqUj0+1rnfI2YCJjOSWmcp0wzRNKBdL/5fsVY=
			</data>
		</dict>
		<key>Headers/bio.h</key>
		<dict>
			<key>hash</key>
			<data>
			3e8z1vvxgbXPwmeg+1bRSmgfCG8=
			</data>
			<key>hash2</key>
			<data>
			eLBVVVlX7sK/tM5fkbJ73P33MSgCa+nOhfzKASb9zT0=
			</data>
		</dict>
		<key>Headers/bioerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			N0rVDI9qQwyDEWvlqW1vGQrh0RU=
			</data>
			<key>hash2</key>
			<data>
			rSn+TvN/18SyVraQyvk3GnTimYH2VhuUQ22sVKYb90s=
			</data>
		</dict>
		<key>Headers/blowfish.h</key>
		<dict>
			<key>hash</key>
			<data>
			BLqJpLWCl4Gl0DR4WO0luoyixMg=
			</data>
			<key>hash2</key>
			<data>
			gTssfP+Ii1twm5DU2lhK/vWbXOHqiIYX1oLxr0hXj68=
			</data>
		</dict>
		<key>Headers/bn.h</key>
		<dict>
			<key>hash</key>
			<data>
			zwKCI0fcYjm7MT9ZsUzGG4BDLTE=
			</data>
			<key>hash2</key>
			<data>
			j3mynFrUeRhqyq6WKlmNmGjcQ0/V+kmgCO7Pr/G5mMo=
			</data>
		</dict>
		<key>Headers/bnerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			ybn9OmsYdCvnSKGlXSQQJ8b+Y44=
			</data>
			<key>hash2</key>
			<data>
			gJirlUUwfW4snMKK6ZXW/aDolZthZ8wD1ncWsCq7J/8=
			</data>
		</dict>
		<key>Headers/buffer.h</key>
		<dict>
			<key>hash</key>
			<data>
			70BHajzy0JGUYwo7F+dpD0CeWrs=
			</data>
			<key>hash2</key>
			<data>
			pPopK5A8uMLOF2G6SYDPW9+2bcrcu1yK7MRbASvGTCM=
			</data>
		</dict>
		<key>Headers/buffererr.h</key>
		<dict>
			<key>hash</key>
			<data>
			PSVdu9joU+POeEegSPtAPctXWyI=
			</data>
			<key>hash2</key>
			<data>
			ci9uqHcRt8DLbuKcFXYsKDnlrRe571ebboG2Eh8DXDA=
			</data>
		</dict>
		<key>Headers/camellia.h</key>
		<dict>
			<key>hash</key>
			<data>
			R0cxfQe4VMejfw/FBnV5jlrTxS8=
			</data>
			<key>hash2</key>
			<data>
			VhvbLphUWOgJ5lbGDkvIPh1nB3Rt2LW628ELZBmNR3A=
			</data>
		</dict>
		<key>Headers/cast.h</key>
		<dict>
			<key>hash</key>
			<data>
			tg9fweKyld2MF5c1iw7sEh5btDM=
			</data>
			<key>hash2</key>
			<data>
			oUxRoSmzrleV2v2YBlvguwy/ALTUUo6Frb9imWb0L1M=
			</data>
		</dict>
		<key>Headers/cmac.h</key>
		<dict>
			<key>hash</key>
			<data>
			SsfJcPvnO3RZ7i+QyWeuyIBoFr4=
			</data>
			<key>hash2</key>
			<data>
			db+V0doRj/Hi8UPMt6Vk2UneRACXuXpJclwDl24We1I=
			</data>
		</dict>
		<key>Headers/cms.h</key>
		<dict>
			<key>hash</key>
			<data>
			WhfyS38eVlM/0EjH+SRNVL1fhB0=
			</data>
			<key>hash2</key>
			<data>
			B+Swp3nJV++QJtacDLWmId3CBgr5Z+DdXpGshh/fvbI=
			</data>
		</dict>
		<key>Headers/cmserr.h</key>
		<dict>
			<key>hash</key>
			<data>
			icv8DehBtI8fn3XrZvJ8GaK3Nm8=
			</data>
			<key>hash2</key>
			<data>
			fg/dq6hfs9ESYq7ixgJyWS3uGmI0pSqUsGuv0cClQ1A=
			</data>
		</dict>
		<key>Headers/comp.h</key>
		<dict>
			<key>hash</key>
			<data>
			iXVcuTWmFCf9QLEmp9sNP8JQUw0=
			</data>
			<key>hash2</key>
			<data>
			mKh3xicj5gQuTkdAzRpud5nfdS2SNDhtc6KKCY5usxw=
			</data>
		</dict>
		<key>Headers/comperr.h</key>
		<dict>
			<key>hash</key>
			<data>
			smXqglFOEgRt0CXetpBcXnWbZYI=
			</data>
			<key>hash2</key>
			<data>
			9kviBdCK8lVxh+wZwDZ4qp4p9m5CjeKc691izXw7VRU=
			</data>
		</dict>
		<key>Headers/conf.h</key>
		<dict>
			<key>hash</key>
			<data>
			lY422quRPGGoMtvoZu73RVfY38s=
			</data>
			<key>hash2</key>
			<data>
			5UNB4wUj2twJ2CE2b1r7E8kobVQLNMGUVAaRjtWI+iE=
			</data>
		</dict>
		<key>Headers/conf_api.h</key>
		<dict>
			<key>hash</key>
			<data>
			aEkI7MCNJGZ+SJxs514uMY1oW3s=
			</data>
			<key>hash2</key>
			<data>
			F1+GtptYsM0dpsmyfYerTk7VLzfcdreJXCEMQ0aVBOs=
			</data>
		</dict>
		<key>Headers/conferr.h</key>
		<dict>
			<key>hash</key>
			<data>
			HhxJvI2jMpKEWKwpMC+m/3YD5tE=
			</data>
			<key>hash2</key>
			<data>
			q/4mSFzUOoDgxHbmKJeWEq0opjVXe69o7uR2hQQlpJg=
			</data>
		</dict>
		<key>Headers/crypto.h</key>
		<dict>
			<key>hash</key>
			<data>
			9mlWjKYcqA63rFf05YOE/OnlLlE=
			</data>
			<key>hash2</key>
			<data>
			0s2E5pXmgFH+Z/p8+QHMuk+tzMcaazAJDfIKAbkdPsc=
			</data>
		</dict>
		<key>Headers/cryptoerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			rvUn24W+SZM/y6GTW6NG1DW8KQo=
			</data>
			<key>hash2</key>
			<data>
			51LX09oypsAJzyZEUHJjZ/1p58KkwYXVgM5lACHWjno=
			</data>
		</dict>
		<key>Headers/ct.h</key>
		<dict>
			<key>hash</key>
			<data>
			cpeLfPNApLUZ414BRmIcahaW0y0=
			</data>
			<key>hash2</key>
			<data>
			snqvDTlglQC0/OOztl9nUhFqyzBCDeHVOUMHSkgQQ/A=
			</data>
		</dict>
		<key>Headers/cterr.h</key>
		<dict>
			<key>hash</key>
			<data>
			z6hnmIDS0tby0u7XWscijJOnipQ=
			</data>
			<key>hash2</key>
			<data>
			q25wtS40EkfEsheIOYD/HpjkRT0QILCwOM2/CWxKuKU=
			</data>
		</dict>
		<key>Headers/des.h</key>
		<dict>
			<key>hash</key>
			<data>
			znOw/0Vq2B1QWQ5QlySAEIkrdwE=
			</data>
			<key>hash2</key>
			<data>
			PLaPu0mONOGeRvnUFWFZaracz3ix2L3y8xLoksBgAV8=
			</data>
		</dict>
		<key>Headers/dh.h</key>
		<dict>
			<key>hash</key>
			<data>
			9zO15LRHFl5v1MTrCuS3Q2iGGu0=
			</data>
			<key>hash2</key>
			<data>
			RtDEiea4BjeSz52Bk/UnOPDY/spOuERV4KNZY21BqeA=
			</data>
		</dict>
		<key>Headers/dherr.h</key>
		<dict>
			<key>hash</key>
			<data>
			PTwLUzp4LhfTwc/VMpRyT9YTuRU=
			</data>
			<key>hash2</key>
			<data>
			cpkNxez4V74yHIi5IVAshTZX+AnsU+r5GgEcCZqFdw0=
			</data>
		</dict>
		<key>Headers/dsa.h</key>
		<dict>
			<key>hash</key>
			<data>
			AEYyYVu2fy7yPpiyRyjyypHNCKI=
			</data>
			<key>hash2</key>
			<data>
			FSXlrfJCZDp9NjElgDFPXOYeyt+CIjpJ/1wyIg14+bk=
			</data>
		</dict>
		<key>Headers/dsaerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			jmOqsG1TSs1yTIm74Qstlz/KODg=
			</data>
			<key>hash2</key>
			<data>
			ZeqLR8slroLWETN81H2nwcXNuUUZgr7snYQUE9SpI6k=
			</data>
		</dict>
		<key>Headers/dtls1.h</key>
		<dict>
			<key>hash</key>
			<data>
			fDDC+ixg7o1tDB4basD847J6+Zk=
			</data>
			<key>hash2</key>
			<data>
			cwTxfMmZi9fBZTmuml6lRfLgxkCG1eaIyroQS2p//sw=
			</data>
		</dict>
		<key>Headers/e_os2.h</key>
		<dict>
			<key>hash</key>
			<data>
			lavqS62GkOuhGihyu+WOjVd92SM=
			</data>
			<key>hash2</key>
			<data>
			DWGWKc2z+15UMR6KP/YC+DjEpFMKVr4/0KgefosQy4Q=
			</data>
		</dict>
		<key>Headers/ebcdic.h</key>
		<dict>
			<key>hash</key>
			<data>
			z5Fn9TbPaQo8zoY+Uwo/lSr9SJ8=
			</data>
			<key>hash2</key>
			<data>
			IonxTxHnWsc50RI2UdFuhB88dONtqkg8H+2fjFwURyA=
			</data>
		</dict>
		<key>Headers/ec.h</key>
		<dict>
			<key>hash</key>
			<data>
			VDanIs80uH9tNKL+PSMmYL0YIu4=
			</data>
			<key>hash2</key>
			<data>
			TmJAZFMZhlYHifhQnf0QvF7YLrZVmeA+07/tV75t8Zk=
			</data>
		</dict>
		<key>Headers/ecdh.h</key>
		<dict>
			<key>hash</key>
			<data>
			iXUqwjlcXCix2i1+T/zXRV4PU18=
			</data>
			<key>hash2</key>
			<data>
			lfuJrdO9MrbUPc8aUdGDm5Fed01xOK+vYY5pDvv0FLs=
			</data>
		</dict>
		<key>Headers/ecdsa.h</key>
		<dict>
			<key>hash</key>
			<data>
			iXUqwjlcXCix2i1+T/zXRV4PU18=
			</data>
			<key>hash2</key>
			<data>
			lfuJrdO9MrbUPc8aUdGDm5Fed01xOK+vYY5pDvv0FLs=
			</data>
		</dict>
		<key>Headers/ecerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			GyM9ZofjjCqJIe+IbO9j4p+HDZk=
			</data>
			<key>hash2</key>
			<data>
			haO2TAZEQgoYKVyAAaBrrV18PoTU+HHJJaG0X7ie8YI=
			</data>
		</dict>
		<key>Headers/engine.h</key>
		<dict>
			<key>hash</key>
			<data>
			dS4eXHBYjCoSL3nDXCYuyZR9V9c=
			</data>
			<key>hash2</key>
			<data>
			qY3weurFeSk7GG6mHDXtzj7r3h/QPYVTVj8YAj3kfJU=
			</data>
		</dict>
		<key>Headers/engineerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			H6oGO47GeHqzTYa913WPlF5cZzQ=
			</data>
			<key>hash2</key>
			<data>
			bz0ekbobeXGSlTDLisPvk0wLgqjNRZyvIk4kGj5a1uk=
			</data>
		</dict>
		<key>Headers/err.h</key>
		<dict>
			<key>hash</key>
			<data>
			OZsmLZ7UryVhV7QY17J5cymnWlY=
			</data>
			<key>hash2</key>
			<data>
			FEcmPwqEDoE1xYbhbD2FjO6Tne3fT9kF45GGmAm02qI=
			</data>
		</dict>
		<key>Headers/evp.h</key>
		<dict>
			<key>hash</key>
			<data>
			Ue0l0jetFcNRP+2k4ve5l9kWsOw=
			</data>
			<key>hash2</key>
			<data>
			nZY73agYApiBcGY8z870N66mkEL0Ku86EatKIXuACu8=
			</data>
		</dict>
		<key>Headers/evperr.h</key>
		<dict>
			<key>hash</key>
			<data>
			YPfW2qvVYScvnOl+vKfUlzf4pvg=
			</data>
			<key>hash2</key>
			<data>
			RdaLRi8lI5SIwCC+5fv2lwCwzXtNwrO6cBW93EmUdqQ=
			</data>
		</dict>
		<key>Headers/hmac.h</key>
		<dict>
			<key>hash</key>
			<data>
			oNXOm72CsIiR3i2/7dzmPfXHpnk=
			</data>
			<key>hash2</key>
			<data>
			djhsInPxBfVMV0ny/IVFc8NxqxhfkkjOKVp0jhJsqug=
			</data>
		</dict>
		<key>Headers/idea.h</key>
		<dict>
			<key>hash</key>
			<data>
			9OhfGjNERiWm+IaFZng3mj74a70=
			</data>
			<key>hash2</key>
			<data>
			uraCxcwGAnyC0SbtjmXX3Z3MdfpGTMvJXX8Wi9accy0=
			</data>
		</dict>
		<key>Headers/kdf.h</key>
		<dict>
			<key>hash</key>
			<data>
			8bAA2Z5x72kQuKB9lY8B07YpOvA=
			</data>
			<key>hash2</key>
			<data>
			qq/UqWK6m58qJGIHbH97SZHZ2H5773HYfpMIEl4OKcA=
			</data>
		</dict>
		<key>Headers/kdferr.h</key>
		<dict>
			<key>hash</key>
			<data>
			eBl7XkjAByV3fh1j+2ZSsSVdPd0=
			</data>
			<key>hash2</key>
			<data>
			5AbLyKinlWvLDgapSUVLSNJwIznK9KLmIawM3pwkY+M=
			</data>
		</dict>
		<key>Headers/lhash.h</key>
		<dict>
			<key>hash</key>
			<data>
			gsp3p9sYfgyLm8w777aVi7LZzaA=
			</data>
			<key>hash2</key>
			<data>
			EDbiCroA4FhblrkaAK53ktwSUBFg4RfR+CSDPef+N1I=
			</data>
		</dict>
		<key>Headers/md2.h</key>
		<dict>
			<key>hash</key>
			<data>
			SU5g+hFH8KXJwSElUE6qnz88XbQ=
			</data>
			<key>hash2</key>
			<data>
			nijwqNXiKXZJrymPXjIJ0y/pSG7bF5PMaYp1fJEnBmI=
			</data>
		</dict>
		<key>Headers/md4.h</key>
		<dict>
			<key>hash</key>
			<data>
			NVmYVdXaFSHylpRJRh52LUqSAIY=
			</data>
			<key>hash2</key>
			<data>
			A9P8nc7sbBaNIZ659ATKCLR41VIWIoNLwkt9RZRXAuQ=
			</data>
		</dict>
		<key>Headers/md5.h</key>
		<dict>
			<key>hash</key>
			<data>
			8R2dids4HGec0BuJ5RjnI0sNAqs=
			</data>
			<key>hash2</key>
			<data>
			oGEqj35p47wWb8GGykS+HlsdAgtdqLW+Ah9z1IwnC4I=
			</data>
		</dict>
		<key>Headers/mdc2.h</key>
		<dict>
			<key>hash</key>
			<data>
			ceP5kO5gOJDJGS7HrDRjpWWG2i4=
			</data>
			<key>hash2</key>
			<data>
			8TsB7AnEXCY0Zz0bmxp5rb1uwyvf+UKHMI4rsnQI5Tc=
			</data>
		</dict>
		<key>Headers/modes.h</key>
		<dict>
			<key>hash</key>
			<data>
			oykGDYKdMkpM+dh+C5148xyIG9A=
			</data>
			<key>hash2</key>
			<data>
			9OUnmH4paib8HAb0+Ja6pfRXt+Z/lJV9F27tRpsP5gI=
			</data>
		</dict>
		<key>Headers/obj_mac.h</key>
		<dict>
			<key>hash</key>
			<data>
			0xczM7tqfkjs0KVU5TgD3MmB0O8=
			</data>
			<key>hash2</key>
			<data>
			gevKIMw3DM9y8xA9EjHGpC4GRzC7IiYal9ht3Q57wIo=
			</data>
		</dict>
		<key>Headers/objects.h</key>
		<dict>
			<key>hash</key>
			<data>
			1BKkzZQ86VAQYSD2wS3W+kAe8lU=
			</data>
			<key>hash2</key>
			<data>
			QBjXxU5vGcMjCvBj9NvIUj8UUFKKSvFal6Ch/z+zujg=
			</data>
		</dict>
		<key>Headers/objectserr.h</key>
		<dict>
			<key>hash</key>
			<data>
			62CLZz09xeqGq4nUpt1HfuvRrbM=
			</data>
			<key>hash2</key>
			<data>
			DsczDBIq4rMXTfld3s6mX/ZhphUs0BklKd0UEe8/YqE=
			</data>
		</dict>
		<key>Headers/ocsp.h</key>
		<dict>
			<key>hash</key>
			<data>
			/vPykCbZnxZ/oD4x73y3vCFlfAg=
			</data>
			<key>hash2</key>
			<data>
			z7vDQ0tWxGcQgDJq64w9AH2rfSw2IdUUbBGro0cVi4A=
			</data>
		</dict>
		<key>Headers/ocsperr.h</key>
		<dict>
			<key>hash</key>
			<data>
			HzVTI0XxPNFGeTAYcha32Bf1qLw=
			</data>
			<key>hash2</key>
			<data>
			xzpXsZGWAf+MBPLcnGLb0TDvLTrjm7D83yW8nm6vMno=
			</data>
		</dict>
		<key>Headers/opensslconf.h</key>
		<dict>
			<key>hash</key>
			<data>
			yNGszHsg1rO96Mwyt+XRNUDMH3E=
			</data>
			<key>hash2</key>
			<data>
			TobY/gSgSFAftkR9MCoN++X6EOEwraLOKY4xULr1+Zo=
			</data>
		</dict>
		<key>Headers/opensslv.h</key>
		<dict>
			<key>hash</key>
			<data>
			RV0inZj+UfBzOwFBUF97hvsSAjg=
			</data>
			<key>hash2</key>
			<data>
			DKEsKrqL++JMTAaGHp1XuTnF5y0cCPN5ikQtaY3rdZk=
			</data>
		</dict>
		<key>Headers/ossl_typ.h</key>
		<dict>
			<key>hash</key>
			<data>
			/hD+IfNYoPQpFE2wIl8jHi6BRVI=
			</data>
			<key>hash2</key>
			<data>
			RvVBocxJvwiQirO94F4mg/xW6iaMo+fy4nYoZT7LShQ=
			</data>
		</dict>
		<key>Headers/pem.h</key>
		<dict>
			<key>hash</key>
			<data>
			32ly0pNLPzdTYGECzCbGY6huGuw=
			</data>
			<key>hash2</key>
			<data>
			MtYgNtDDXQP+f33ar/48rq/PmE7Bbk237RnT4gLp17o=
			</data>
		</dict>
		<key>Headers/pem2.h</key>
		<dict>
			<key>hash</key>
			<data>
			t2jj9ipWiYEJZvgm7/w68FCv7xk=
			</data>
			<key>hash2</key>
			<data>
			aDPuVxISXRzo97UkN6dS5AwvR5MnaFmoIowN5x01w+A=
			</data>
		</dict>
		<key>Headers/pemerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			LPK/t/8oy0h9S1TUlxQnvG640ps=
			</data>
			<key>hash2</key>
			<data>
			2RxgFvPBunBoO5L95VUA7DNHd977ZIbEGwqcTkAqopU=
			</data>
		</dict>
		<key>Headers/pkcs12.h</key>
		<dict>
			<key>hash</key>
			<data>
			Wgtt5Ajf2I0Qe0Yw/C6/da673Vs=
			</data>
			<key>hash2</key>
			<data>
			l8Epxsmkk9fl068SPZYEDYfE5U/F5BrsRQgyyzKmNNY=
			</data>
		</dict>
		<key>Headers/pkcs12err.h</key>
		<dict>
			<key>hash</key>
			<data>
			iYGY/k8EiROz+H6/d3nIzlOnXHY=
			</data>
			<key>hash2</key>
			<data>
			tzQHJ6BIDzUYI7Zp9VzTmJwGXTc8jPb/h0W4NW9h1f8=
			</data>
		</dict>
		<key>Headers/pkcs7.h</key>
		<dict>
			<key>hash</key>
			<data>
			DSVKNOqJWVpNEj9g1afFP/AAHbs=
			</data>
			<key>hash2</key>
			<data>
			zcNQXJu5FopvrUNN2dmkmtYwua6SFr1mWxEFHgSnCbw=
			</data>
		</dict>
		<key>Headers/pkcs7err.h</key>
		<dict>
			<key>hash</key>
			<data>
			vbVkdgXZDNlmUBjHsDcvjoyWNTE=
			</data>
			<key>hash2</key>
			<data>
			7O4R6fvdqyD3igGACeai2vKH/13wBnkpixN/6Zltk4Y=
			</data>
		</dict>
		<key>Headers/rand.h</key>
		<dict>
			<key>hash</key>
			<data>
			BUI/kpuBobNUPlXrW/OkHeq9ClM=
			</data>
			<key>hash2</key>
			<data>
			WpGry90U3PL8YnYczFjulAcnTqVYwpL6d61iUgSsK9c=
			</data>
		</dict>
		<key>Headers/rand_drbg.h</key>
		<dict>
			<key>hash</key>
			<data>
			dZJZsnraib5Oxt12qSJ4bXxP3/w=
			</data>
			<key>hash2</key>
			<data>
			EjecwKfxaMv/jgiCjactwOh3c7xsO9FMS1dQYzm2H6U=
			</data>
		</dict>
		<key>Headers/randerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			/b3o6sjMLxkewJmLW7LeYF/10A0=
			</data>
			<key>hash2</key>
			<data>
			Qfzr+wdnr6A/PeJzLT8M5GzSNn7DSyAWzbXJ6EqaiZQ=
			</data>
		</dict>
		<key>Headers/rc2.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xzwvx1iv4W35klxWCpyRR35/Uwc=
			</data>
			<key>hash2</key>
			<data>
			7CuRlomLvEX/KrACBPk6byDJdCJVEPKQl61ppu7rzf4=
			</data>
		</dict>
		<key>Headers/rc4.h</key>
		<dict>
			<key>hash</key>
			<data>
			01mH39v8pvXIdzB3N/3bn0uJwVs=
			</data>
			<key>hash2</key>
			<data>
			b8AjRC9SQ0loXRPVCFStdzsSuMehU9ctYVqyfdSj1gk=
			</data>
		</dict>
		<key>Headers/rc5.h</key>
		<dict>
			<key>hash</key>
			<data>
			nVMdNFdbOheiSzNQjJ5v92LvEmI=
			</data>
			<key>hash2</key>
			<data>
			wjiVSh3yP1I2LW5fp43yx6WirW75U25InwojKV76DKs=
			</data>
		</dict>
		<key>Headers/ripemd.h</key>
		<dict>
			<key>hash</key>
			<data>
			UUNVXGUU1UnsGpXivIzpc/ZyFQs=
			</data>
			<key>hash2</key>
			<data>
			jHS5PBCp6Dq8F87TqAIa91BvOaD62rB7XbLU+uvPaLY=
			</data>
		</dict>
		<key>Headers/rsa.h</key>
		<dict>
			<key>hash</key>
			<data>
			BXOED7wh3elZhA9q8KoFrXffhUg=
			</data>
			<key>hash2</key>
			<data>
			UJxNuQgRlc9snl/UaDiQrrOVCfKZehmJ3VpXz0MDm8I=
			</data>
		</dict>
		<key>Headers/rsaerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			jrv7NJXCaI3Ppho9VNnyZFn9jxQ=
			</data>
			<key>hash2</key>
			<data>
			4DKpM7GkwT7JAddWGtI6zgGIG2DNzIavDkywoloqMlI=
			</data>
		</dict>
		<key>Headers/safestack.h</key>
		<dict>
			<key>hash</key>
			<data>
			Cd9WYSUSt9OnXah9XHF3QevCc+E=
			</data>
			<key>hash2</key>
			<data>
			V2Zdq7N/TwvYU1Odk8ZMuK3zf9lVLcn60hXMj0epkcQ=
			</data>
		</dict>
		<key>Headers/seed.h</key>
		<dict>
			<key>hash</key>
			<data>
			xIqmrORS9667meS1VHouBNO1FGI=
			</data>
			<key>hash2</key>
			<data>
			a/I3PfsQ3Fy8Ymzy/oa5scgjc9eZvda+E+7a99RUDVU=
			</data>
		</dict>
		<key>Headers/sha.h</key>
		<dict>
			<key>hash</key>
			<data>
			lu1HA4odImsyOAN6vcDKaHOxMrc=
			</data>
			<key>hash2</key>
			<data>
			C7Z0VIGsVrZ/RQ0JAz6BO/j2pfICXpDV61OeqxrV4yM=
			</data>
		</dict>
		<key>Headers/shim.h</key>
		<dict>
			<key>hash</key>
			<data>
			sMxnt7+rU4Gir29aFtcGAwjCdhs=
			</data>
			<key>hash2</key>
			<data>
			Ra475POOCRP20DOT0uDAvv/CkGhwldG3+hjPVXYZ3Ws=
			</data>
		</dict>
		<key>Headers/sm2.h</key>
		<dict>
			<key>hash</key>
			<data>
			J9BR3kk+bjp4DUXqRy3dTOnScEM=
			</data>
			<key>hash2</key>
			<data>
			K7FK9nwl8cJHxILCnHbOuVWycEoqpMFU3fiLPSY8/04=
			</data>
		</dict>
		<key>Headers/sm3.h</key>
		<dict>
			<key>hash</key>
			<data>
			H8hFoehBM7TTiupNtJpraPdF6TE=
			</data>
			<key>hash2</key>
			<data>
			RDSKyY74qd98l7DUslt8I5iPjP24RzWiakKvFsPoZmU=
			</data>
		</dict>
		<key>Headers/sm4.h</key>
		<dict>
			<key>hash</key>
			<data>
			u11NBly6iFRmqITEgK0HhoQVACA=
			</data>
			<key>hash2</key>
			<data>
			qj1uC4OsMfDYcvAZmMd1mc/ElaNU336uYHe5RKe5ja8=
			</data>
		</dict>
		<key>Headers/srp.h</key>
		<dict>
			<key>hash</key>
			<data>
			hs0FaZQgqkGx2ATJtkZLR3E7ajQ=
			</data>
			<key>hash2</key>
			<data>
			gqCL+ahm3sG33rZrQHdpDO4PbK+R6wATbF7tTo2UPQY=
			</data>
		</dict>
		<key>Headers/srtp.h</key>
		<dict>
			<key>hash</key>
			<data>
			Xiev9lzpqvNNo1eXz+UZl4RZh+0=
			</data>
			<key>hash2</key>
			<data>
			5Q4t1d9qDbIZCRzRxnaKbTGe9khbFuHzYfzkMGeEdiY=
			</data>
		</dict>
		<key>Headers/ssl.h</key>
		<dict>
			<key>hash</key>
			<data>
			mhalYjN/qeuEF+l6WIVquvXvjOs=
			</data>
			<key>hash2</key>
			<data>
			Z+hVP6OlpyPzsPKff23VH/dbSj1OeZ6x2Y710a9gnlI=
			</data>
		</dict>
		<key>Headers/ssl2.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZUBF9zzQr/EnSx9hH0JCB0ETP8s=
			</data>
			<key>hash2</key>
			<data>
			f7VXoySIrUSiVCCr/4J5q9C9H0q3aOc9Ph1cLas2wMU=
			</data>
		</dict>
		<key>Headers/ssl3.h</key>
		<dict>
			<key>hash</key>
			<data>
			cA/eJmiVcOy62x5KksvM5fafytI=
			</data>
			<key>hash2</key>
			<data>
			0Ez+wqn52iqimfVYhCFeIAtJCm4KlCMlUmJki9im0cA=
			</data>
		</dict>
		<key>Headers/sslerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			4EiOgpXMaHQuyZqwDCHkMoCYXhw=
			</data>
			<key>hash2</key>
			<data>
			VKPHhOv6tjHSG7m32zzjgWeswCmeiSC1zm5Bmjc2kwo=
			</data>
		</dict>
		<key>Headers/stack.h</key>
		<dict>
			<key>hash</key>
			<data>
			HlhyAUEwF7fsEBjOpYfB+ZGw2SU=
			</data>
			<key>hash2</key>
			<data>
			RbqAO74UAH5JS7JLLKlU9jYu9wChBIDvo9LzrLFZ3uw=
			</data>
		</dict>
		<key>Headers/store.h</key>
		<dict>
			<key>hash</key>
			<data>
			f3f0erlSosQY7bDqrHLGzKe7YY4=
			</data>
			<key>hash2</key>
			<data>
			EQW60aMJ0xIqLPvcQJijPjPVDIEY5wuzMvO31u8ru1g=
			</data>
		</dict>
		<key>Headers/storeerr.h</key>
		<dict>
			<key>hash</key>
			<data>
			kv/pt/4V9S0com6azZDBL4S2ef4=
			</data>
			<key>hash2</key>
			<data>
			BxxmvMA+1cKiTxlk9F6NGmM/PLSxg8cY3tPiUxL4xOA=
			</data>
		</dict>
		<key>Headers/symhacks.h</key>
		<dict>
			<key>hash</key>
			<data>
			9immsicIban6k2s+1MF7N3ITwzE=
			</data>
			<key>hash2</key>
			<data>
			DJ4CalkyokMuPN9977eJYQxCcgEOUeX/BHGAnqu3qi0=
			</data>
		</dict>
		<key>Headers/tls1.h</key>
		<dict>
			<key>hash</key>
			<data>
			snV9T38ogNzmGbcPr9Rn32lFHOo=
			</data>
			<key>hash2</key>
			<data>
			1BBMqHIDMrmFL0cl1GYKbPd6UrWH59luwmPplsHQri8=
			</data>
		</dict>
		<key>Headers/ts.h</key>
		<dict>
			<key>hash</key>
			<data>
			iWDV/jVLCzacuMrj1s833l3wAYY=
			</data>
			<key>hash2</key>
			<data>
			m9A568579rbnH8lmfkTgF/wMt8ecAjvhyWWJTmG3kjg=
			</data>
		</dict>
		<key>Headers/tserr.h</key>
		<dict>
			<key>hash</key>
			<data>
			PI3IsQNlWi/RwHMUbbL1VJDUvj0=
			</data>
			<key>hash2</key>
			<data>
			tuG7yMU+T3wFR2jexVJy0AHfv+54ioW6iwwGngjLvoU=
			</data>
		</dict>
		<key>Headers/txt_db.h</key>
		<dict>
			<key>hash</key>
			<data>
			NoTeslBGWtynVU+GDhyxyzXUdTE=
			</data>
			<key>hash2</key>
			<data>
			kDaWvVuZCFMPioV4/vR3IfR/P+hQe6x3YUc+kl6UIVk=
			</data>
		</dict>
		<key>Headers/ui.h</key>
		<dict>
			<key>hash</key>
			<data>
			++9liaOBXfJY40d+p4saEce2zpw=
			</data>
			<key>hash2</key>
			<data>
			9eo/XS576z6B24wjxuYY445RHCE/2ToR71G5rkrQNhk=
			</data>
		</dict>
		<key>Headers/uierr.h</key>
		<dict>
			<key>hash</key>
			<data>
			R61EyGTilaauZmnHry4jjFiN5ko=
			</data>
			<key>hash2</key>
			<data>
			uzk3fHAtl2VUfOlfnnWLRqVO5OJon4kgM88qTqV9XQs=
			</data>
		</dict>
		<key>Headers/whrlpool.h</key>
		<dict>
			<key>hash</key>
			<data>
			Uo0K/dGVqhsRUor/8CFpmWNaoHY=
			</data>
			<key>hash2</key>
			<data>
			OHUHItFzcIP6jK7fzNO85XQIDjVpIBDS+R3TA7FUr2k=
			</data>
		</dict>
		<key>Headers/x509.h</key>
		<dict>
			<key>hash</key>
			<data>
			5wWbXaF8/6/pZR0i8O64cxA2vYY=
			</data>
			<key>hash2</key>
			<data>
			8bizVtZJ2iQjbVqr36/gR7UWsgCNjV0J2l6Z+8S0qYw=
			</data>
		</dict>
		<key>Headers/x509_vfy.h</key>
		<dict>
			<key>hash</key>
			<data>
			+5MyYNetMS2YX3CRFqSzMEZc4TE=
			</data>
			<key>hash2</key>
			<data>
			9iGsTDiom9AQZWygCTd3MSan7zSLKaFLBMxexcCVtTE=
			</data>
		</dict>
		<key>Headers/x509err.h</key>
		<dict>
			<key>hash</key>
			<data>
			z4/1nUkbgTSuqRKSBA/+tS9zTD4=
			</data>
			<key>hash2</key>
			<data>
			YiSZuaqlRpV8gC2pruNnlMJMnH0uEFrmm0eEHs2sa3o=
			</data>
		</dict>
		<key>Headers/x509v3.h</key>
		<dict>
			<key>hash</key>
			<data>
			YhR8NOnXe50FuvmDc73j2oiOZGo=
			</data>
			<key>hash2</key>
			<data>
			p+eKGFhpfnWUC0FroftvThNXIuiMDCgK+Kl5l1xUQ9o=
			</data>
		</dict>
		<key>Headers/x509v3err.h</key>
		<dict>
			<key>hash</key>
			<data>
			X1T6LneJ+WEeudGjNgNYP08RqMU=
			</data>
			<key>hash2</key>
			<data>
			PRfcyid81vY3OjCm4H4aLEQCSguYDMzMJTPXi/DCJ3Y=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			wSMjVnQJnXCQkrl0p+m5ijRHe3c=
			</data>
			<key>hash2</key>
			<data>
			Iyk3YE2iKlYEZnIdRRtNf4piXHBV2iLaIdaHtxJFxL8=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			gyD3g6vYmIQTZWlTzAiL16f0sAs=
			</data>
			<key>hash2</key>
			<data>
			GNE6hZnY2rOyEomQAkzfXjz/sqCLiQ3u9KQPCjciQV8=
			</data>
		</dict>
		<key>Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			AL1dh5ctObXBjoBiabSJ86M3HQs=
			</data>
			<key>hash2</key>
			<data>
			WpuPwM3bECAbtHzCgEs/AExyUUdmItJb/E61TtRuEIQ=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
