#
# Generated file, do not edit.
#

Pod::Spec.new do |s|
  s.name             = 'FlutterPluginRegistrant'
  s.version          = '0.0.1'
  s.summary          = 'Registers plugins with your Flutter app'
  s.description      = <<-DESC
Depends on all your plugins, and provides a function to register them.
                       DESC
  s.homepage         = 'https://flutter.dev'
  s.license          = { :type => 'BSD' }
  s.author           = { 'Flutter Dev Team' => '<EMAIL>' }
  s.ios.deployment_target = '12.0'
  s.source_files =  "Classes", "Classes/**/*.{h,m}"
  s.source           = { :path => '.' }
  s.public_header_files = './Classes/**/*.h'
  s.static_framework    = true
  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES' }
  s.dependency 'Flutter'
  s.dependency 'device_info'
  s.dependency 'flutter_secure_storage'
  s.dependency 'fluttertoast'
  s.dependency 'image_picker_ios'
  s.dependency 'package_info'
  s.dependency 'rrworkmanager'
  s.dependency 'shared_preferences_foundation'
  s.dependency 'video_player_avfoundation'
  s.dependency 'wakelock'
  s.dependency 'webview_flutter_wkwebview'
end
