_fe_analyzer_shared
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-85.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-85.0.0/lib/
analyzer
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-7.5.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-7.5.9/lib/
args
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/lib/
asn1lib
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/asn1lib-1.6.5/lib/
async
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/
azlistview
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/azlistview-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/azlistview-2.0.0/lib/
boolean_selector
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/lib/
build
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.5.4/lib/
build_config
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/lib/
build_daemon
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/lib/
build_resolvers
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.5.4/lib/
build_runner
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.5.4/lib/
build_runner_core
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-9.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-9.1.2/lib/
built_collection
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/lib/
built_value
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1/lib/
characters
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/
checked_yaml
3.8
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/lib/
chewie
2.13
file:///Users/<USER>/.pub-cache/hosted/pub.dev/chewie-1.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/chewie-1.5.0/lib/
chewie_audio
2.13
file:///Users/<USER>/.pub-cache/hosted/pub.dev/chewie_audio-1.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/chewie_audio-1.5.0/lib/
clock
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/
code_builder
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/lib/
collection
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/
convert
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/lib/
cross_file
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/
crypto
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/
csslib
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-0.17.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-0.17.3/lib/
cupertino_icons
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
dart_style
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-3.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-3.1.0/lib/
device_info
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info-2.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info-2.0.3/lib/
device_info_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_platform_interface-2.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_platform_interface-2.0.1/lib/
dio
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-4.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-4.0.6/lib/
easy_localization
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/easy_localization-3.0.7+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/easy_localization-3.0.7+1/lib/
easy_logger
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/easy_logger-0.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/easy_logger-0.0.2/lib/
encrypt
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/encrypt-5.0.3/lib/
fake_async
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/lib/
ffi
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/
file
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/
file_selector_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/
file_selector_macos
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/
file_selector_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/
file_selector_windows
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/
fixnum
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/
flutter_html
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-2.2.1/lib/
flutter_layout_grid
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_layout_grid-1.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_layout_grid-1.0.6/lib/
flutter_math_fork
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_math_fork-0.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_math_fork-0.5.0/lib/
flutter_plugin_android_lifecycle
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib/
flutter_rating_bar
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_rating_bar-4.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_rating_bar-4.0.1/lib/
flutter_screenutil
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.0.0+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.0.0+2/lib/
flutter_secure_storage
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/lib/
flutter_svg
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-0.23.0+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-0.23.0+1/lib/
fluttertoast
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.0.8/lib/
frontend_server_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/lib/
glob
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/lib/
graphs
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/lib/
html
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.4/lib/
http
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/
http_multi_server
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/lib/
http_parser
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/
image_picker
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-0.8.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-0.8.9/lib/
image_picker_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/lib/
image_picker_for_web
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-2.1.12/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-2.1.12/lib/
image_picker_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/
image_picker_linux
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/
image_picker_macos
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/
image_picker_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/
image_picker_windows
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/
intl
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/
io
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/lib/
js
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/lib/
json_annotation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/
leak_tracker
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/lib/
leak_tracker_flutter_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
logging
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/
lpinyin
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lpinyin-2.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lpinyin-2.0.3/lib/
matcher
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/
mime
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/
nested
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/
numerus
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/numerus-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/numerus-1.1.1/lib/
package_config
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/lib/
package_info
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info-2.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info-2.0.2/lib/
path
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/
path_drawing
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_drawing-0.5.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_drawing-0.5.1+1/lib/
path_parsing
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-0.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-0.2.1/lib/
path_provider_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
petitparser
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-5.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-5.4.0/lib/
platform
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
pointycastle
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/lib/
pool
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/lib/
provider
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/
pub_semver
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/lib/
pubspec_parse
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/lib/
pull_to_refresh
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pull_to_refresh-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pull_to_refresh-2.0.0/lib/
quiver
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/quiver-3.2.2/lib/
scrollable_positioned_list
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/scrollable_positioned_list-0.2.3/lib/
shared_preferences
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/
shared_preferences_foundation
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
shelf
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/lib/
shelf_web_socket
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/lib/
source_span
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/
stack_trace
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/
stream_channel
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/
stream_transform
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/
string_scanner
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/
term_glyph
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/lib/
timing
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/lib/
tuple
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/tuple-2.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/tuple-2.0.2/lib/
typed_data
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/
vector_math
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/
video_player
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.10.0/lib/
video_player_android
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/lib/
video_player_avfoundation
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.0/lib/
video_player_platform_interface
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.4.0/lib/
video_player_web
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.4.0/lib/
vm_service
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/lib/
wakelock
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock-0.6.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock-0.6.2/lib/
wakelock_macos
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_macos-0.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_macos-0.4.0/lib/
wakelock_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_platform_interface-0.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_platform_interface-0.3.0/lib/
wakelock_web
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_web-0.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_web-0.4.0/lib/
wakelock_windows
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_windows-0.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_windows-0.2.1/lib/
watcher
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/lib/
web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/
web_socket
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/
web_socket_channel
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/
webview_flutter
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-2.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-2.8.0/lib/
webview_flutter_android
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-2.10.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-2.10.4/lib/
webview_flutter_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-1.9.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-1.9.5/lib/
webview_flutter_wkwebview
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/lib/
win32
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-3.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-3.1.4/lib/
xdg_directories
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
2.16
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-5.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-5.4.1/lib/
yaml
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib/
rrworkmanager
2.16
file:///Users/<USER>/Downloads/xwallet-cusomer-ios-master%202/xWalletPro_iOS/RRWorkManager/
file:///Users/<USER>/Downloads/xwallet-cusomer-ios-master%202/xWalletPro_iOS/RRWorkManager/lib/
xwalletpro_flutter
2.17
file:///Users/<USER>/Downloads/xwallet-cusomer-ios-master%202/xWalletPro_iOS/xwalletpro_flutter/
file:///Users/<USER>/Downloads/xwallet-cusomer-ios-master%202/xWalletPro_iOS/xwalletpro_flutter/lib/
sky_engine
3.7
file:///Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/
file:///Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///Users/<USER>/development/flutter/packages/flutter/
file:///Users/<USER>/development/flutter/packages/flutter/lib/
flutter_localizations
3.7
file:///Users/<USER>/development/flutter/packages/flutter_localizations/
file:///Users/<USER>/development/flutter/packages/flutter_localizations/lib/
flutter_test
3.7
file:///Users/<USER>/development/flutter/packages/flutter_test/
file:///Users/<USER>/development/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///Users/<USER>/development/flutter/packages/flutter_web_plugins/
file:///Users/<USER>/development/flutter/packages/flutter_web_plugins/lib/
2
