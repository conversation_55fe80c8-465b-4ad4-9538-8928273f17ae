import React from 'react';
import { Text } from '@/components/ui';

interface TypographyProps {
  children: React.ReactNode;
  className?: string;
}

// Heading Components
export const H1: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <Text className={`text-3xl font-bold text-gray-900 ${className}`}>
    {children}
  </Text>
);

export const H2: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <Text className={`text-2xl font-bold text-gray-900 ${className}`}>
    {children}
  </Text>
);

export const H3: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <Text className={`text-xl font-semibold text-gray-900 ${className}`}>
    {children}
  </Text>
);

export const H4: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <Text className={`text-lg font-semibold text-gray-900 ${className}`}>
    {children}
  </Text>
);

// Body Text Components
export const BodyLarge: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <Text className={`text-lg text-gray-700 ${className}`}>
    {children}
  </Text>
);

export const BodyMedium: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <Text className={`text-base text-gray-700 ${className}`}>
    {children}
  </Text>
);

export const BodySmall: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <Text className={`text-sm text-gray-600 ${className}`}>
    {children}
  </Text>
);

// Caption and Label Components
export const Caption: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <Text className={`text-xs text-gray-500 ${className}`}>
    {children}
  </Text>
);

export const Label: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <Text className={`text-sm font-medium text-gray-700 ${className}`}>
    {children}
  </Text>
);

// Specialized Text Components
export const ErrorText: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <Text className={`text-sm text-red-600 ${className}`}>
    {children}
  </Text>
);

export const SuccessText: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <Text className={`text-sm text-green-600 ${className}`}>
    {children}
  </Text>
);

export const WarningText: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <Text className={`text-sm text-orange-600 ${className}`}>
    {children}
  </Text>
);

export const InfoText: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <Text className={`text-sm text-blue-600 ${className}`}>
    {children}
  </Text>
);

// Link Component
export const Link: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <Text className={`text-blue-600 underline ${className}`}>
    {children}
  </Text>
);

// Monospace Text (for codes, numbers)
export const MonoText: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <Text className={`font-mono text-gray-800 ${className}`}>
    {children}
  </Text>
);

// Currency Display
interface CurrencyTextProps extends TypographyProps {
  amount: string | number;
  currency?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export const CurrencyText: React.FC<CurrencyTextProps> = ({ 
  amount, 
  currency = 'USD', 
  size = 'md',
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-2xl',
  };

  const formattedAmount = typeof amount === 'number' 
    ? amount.toFixed(2) 
    : parseFloat(amount).toFixed(2);

  return (
    <Text className={`font-semibold text-gray-900 ${sizeClasses[size]} ${className}`}>
      {currency} {formattedAmount}
    </Text>
  );
};

// Status Text with Color Coding
interface StatusTextProps extends TypographyProps {
  status: 'success' | 'pending' | 'failed' | 'cancelled';
}

export const StatusText: React.FC<StatusTextProps> = ({ status, children, className = '' }) => {
  const statusColors = {
    success: 'text-green-600',
    pending: 'text-orange-600',
    failed: 'text-red-600',
    cancelled: 'text-gray-600',
  };

  return (
    <Text className={`text-sm font-medium ${statusColors[status]} ${className}`}>
      {children || status.charAt(0).toUpperCase() + status.slice(1)}
    </Text>
  );
};
