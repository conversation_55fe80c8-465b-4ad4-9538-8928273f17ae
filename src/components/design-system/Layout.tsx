import React from 'react';
import { Box, VStack, <PERSON><PERSON><PERSON>ck, SafeAreaView, ScrollView } from '@/components/ui';

interface LayoutProps {
  children: React.ReactNode;
  className?: string;
}

// Screen Container
export const Screen: React.FC<LayoutProps> = ({ children, className = '' }) => (
  <SafeAreaView className={`flex-1 bg-gray-50 ${className}`}>
    {children}
  </SafeAreaView>
);

// Content Container with Padding
export const Container: React.FC<LayoutProps> = ({ children, className = '' }) => (
  <Box className={`px-6 py-4 ${className}`}>
    {children}
  </Box>
);

// Scrollable Content
export const ScrollableContent: React.FC<LayoutProps> = ({ children, className = '' }) => (
  <ScrollView className={`flex-1 ${className}`} showsVerticalScrollIndicator={false}>
    {children}
  </ScrollView>
);

// Section with Title
interface SectionProps extends LayoutProps {
  title?: string;
  subtitle?: string;
  spacing?: 'sm' | 'md' | 'lg';
}

export const Section: React.FC<SectionProps> = ({ 
  title, 
  subtitle, 
  children, 
  spacing = 'md',
  className = '' 
}) => {
  const spacingClasses = {
    sm: 'space-y-2',
    md: 'space-y-4',
    lg: 'space-y-6',
  };

  return (
    <VStack className={`${spacingClasses[spacing]} ${className}`}>
      {(title || subtitle) && (
        <VStack space="xs">
          {title && (
            <Text className="text-lg font-semibold text-gray-800">{title}</Text>
          )}
          {subtitle && (
            <Text className="text-sm text-gray-600">{subtitle}</Text>
          )}
        </VStack>
      )}
      {children}
    </VStack>
  );
};

// Grid Layout
interface GridProps extends LayoutProps {
  columns?: 2 | 3 | 4;
  spacing?: 'sm' | 'md' | 'lg';
}

export const Grid: React.FC<GridProps> = ({ 
  children, 
  columns = 2, 
  spacing = 'md',
  className = '' 
}) => {
  const spacingClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
  };

  const columnClasses = {
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
  };

  return (
    <Box className={`grid ${columnClasses[columns]} ${spacingClasses[spacing]} ${className}`}>
      {children}
    </Box>
  );
};

// Flex Row with Equal Spacing
export const Row: React.FC<LayoutProps> = ({ children, className = '' }) => (
  <HStack className={`justify-between items-center ${className}`}>
    {children}
  </HStack>
);

// Flex Column with Spacing
interface ColumnProps extends LayoutProps {
  spacing?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  align?: 'start' | 'center' | 'end';
}

export const Column: React.FC<ColumnProps> = ({ 
  children, 
  spacing = 'md',
  align = 'start',
  className = '' 
}) => {
  const spacingClasses = {
    xs: 'space-y-1',
    sm: 'space-y-2',
    md: 'space-y-4',
    lg: 'space-y-6',
    xl: 'space-y-8',
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
  };

  return (
    <VStack className={`${spacingClasses[spacing]} ${alignClasses[align]} ${className}`}>
      {children}
    </VStack>
  );
};

// Center Content
export const Center: React.FC<LayoutProps> = ({ children, className = '' }) => (
  <Box className={`flex-1 items-center justify-center ${className}`}>
    {children}
  </Box>
);

// Spacer
interface SpacerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

export const Spacer: React.FC<SpacerProps> = ({ size = 'md' }) => {
  const sizeClasses = {
    xs: 'h-1',
    sm: 'h-2',
    md: 'h-4',
    lg: 'h-6',
    xl: 'h-8',
  };

  return <Box className={sizeClasses[size]} />;
};

// Divider with Text
interface DividerWithTextProps {
  text: string;
  className?: string;
}

export const DividerWithText: React.FC<DividerWithTextProps> = ({ text, className = '' }) => (
  <HStack className={`items-center ${className}`}>
    <Box className="flex-1 h-px bg-gray-300" />
    <Text className="px-4 text-sm text-gray-500">{text}</Text>
    <Box className="flex-1 h-px bg-gray-300" />
  </HStack>
);

// Sticky Header
export const StickyHeader: React.FC<LayoutProps> = ({ children, className = '' }) => (
  <Box className={`bg-white border-b border-gray-200 ${className}`}>
    {children}
  </Box>
);

// Floating Action Button Container
export const FABContainer: React.FC<LayoutProps> = ({ children, className = '' }) => (
  <Box className={`absolute bottom-6 right-6 ${className}`}>
    {children}
  </Box>
);

// Bottom Sheet Container
export const BottomSheet: React.FC<LayoutProps> = ({ children, className = '' }) => (
  <Box className={`absolute bottom-0 left-0 right-0 bg-white rounded-t-xl shadow-lg ${className}`}>
    {children}
  </Box>
);

// Modal Overlay
export const ModalOverlay: React.FC<LayoutProps> = ({ children, className = '' }) => (
  <Box className={`absolute inset-0 bg-black/50 items-center justify-center ${className}`}>
    {children}
  </Box>
);
