import React, { Component, ReactNode } from 'react';
import { Box, VStack, Text, Button, ButtonText } from '@/components/ui';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <Box className="flex-1 items-center justify-center p-6 bg-gray-50">
          <VStack className="items-center max-w-sm" space="lg">
            <Box className="w-20 h-20 bg-red-100 rounded-full items-center justify-center">
              <Text className="text-red-600 text-3xl">⚠️</Text>
            </Box>
            
            <VStack className="items-center" space="sm">
              <Text className="text-xl font-bold text-gray-800 text-center">
                Something went wrong
              </Text>
              <Text className="text-gray-600 text-center">
                We're sorry, but something unexpected happened. Please try again.
              </Text>
            </VStack>

            {__DEV__ && this.state.error && (
              <Box className="bg-gray-100 p-4 rounded-lg w-full">
                <Text className="text-xs text-gray-700 font-mono">
                  {this.state.error.message}
                </Text>
              </Box>
            )}

            <Button onPress={this.handleRetry} className="w-full">
              <ButtonText>Try Again</ButtonText>
            </Button>
          </VStack>
        </Box>
      );
    }

    return this.props.children;
  }
}
