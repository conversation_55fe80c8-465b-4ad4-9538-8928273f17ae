import React from 'react';
import { ActivityIndicator } from 'react-native';
import { Box, VStack, Text } from '@/components/ui';

interface LoadingSpinnerProps {
  size?: 'small' | 'large';
  color?: string;
  message?: string;
  overlay?: boolean;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'large',
  color = '#007AFF',
  message,
  overlay = false,
}) => {
  const content = (
    <VStack className="items-center justify-center" space="md">
      <ActivityIndicator size={size} color={color} />
      {message && (
        <Text className="text-gray-600 text-center">{message}</Text>
      )}
    </VStack>
  );

  if (overlay) {
    return (
      <Box className="absolute inset-0 bg-black/50 items-center justify-center z-50">
        <Box className="bg-white rounded-lg p-6 mx-8">
          {content}
        </Box>
      </Box>
    );
  }

  return (
    <Box className="flex-1 items-center justify-center p-6">
      {content}
    </Box>
  );
};
