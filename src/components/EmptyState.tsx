import React from 'react';
import { Box, VStack, Text, Button, ButtonText } from '@/components/ui';

interface EmptyStateProps {
  icon?: string;
  title: string;
  description: string;
  actionText?: string;
  onAction?: () => void;
  className?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon = '📭',
  title,
  description,
  actionText,
  onAction,
  className = '',
}) => {
  return (
    <Box className={`flex-1 items-center justify-center p-8 ${className}`}>
      <VStack className="items-center max-w-sm" space="lg">
        <Box className="w-20 h-20 bg-gray-100 rounded-full items-center justify-center">
          <Text className="text-3xl">{icon}</Text>
        </Box>
        
        <VStack className="items-center" space="sm">
          <Text className="text-xl font-bold text-gray-800 text-center">
            {title}
          </Text>
          <Text className="text-gray-600 text-center">
            {description}
          </Text>
        </VStack>

        {actionText && onAction && (
          <Button onPress={onAction} className="w-full">
            <ButtonText>{actionText}</ButtonText>
          </Button>
        )}
      </VStack>
    </Box>
  );
};
