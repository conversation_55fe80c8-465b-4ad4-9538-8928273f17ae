import { useState, useCallback } from 'react';
import { ValidationResult } from '../utils/validation';

interface UseFormOptions<T> {
  initialValues: T;
  validationRules?: Partial<Record<keyof T, (value: any) => ValidationResult>>;
  onSubmit?: (values: T) => void | Promise<void>;
}

interface UseFormReturn<T> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  isValid: boolean;
  isSubmitting: boolean;
  setValue: (field: keyof T, value: any) => void;
  setValues: (values: Partial<T>) => void;
  setError: (field: keyof T, error: string) => void;
  clearError: (field: keyof T) => void;
  clearErrors: () => void;
  validate: (field?: keyof T) => boolean;
  handleSubmit: () => Promise<void>;
  reset: () => void;
}

export function useForm<T extends Record<string, any>>({
  initialValues,
  validationRules = {},
  onSubmit,
}: UseFormOptions<T>): UseFormReturn<T> {
  const [values, setValuesState] = useState<T>(initialValues);
  const [errors, setErrorsState] = useState<Partial<Record<keyof T, string>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const setValue = useCallback((field: keyof T, value: any) => {
    setValuesState(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrorsState(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [errors]);

  const setValues = useCallback((newValues: Partial<T>) => {
    setValuesState(prev => ({ ...prev, ...newValues }));
  }, []);

  const setError = useCallback((field: keyof T, error: string) => {
    setErrorsState(prev => ({ ...prev, [field]: error }));
  }, []);

  const clearError = useCallback((field: keyof T) => {
    setErrorsState(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearErrors = useCallback(() => {
    setErrorsState({});
  }, []);

  const validate = useCallback((field?: keyof T): boolean => {
    if (field) {
      // Validate single field
      const rule = validationRules[field];
      if (rule) {
        const result = rule(values[field]);
        if (!result.isValid) {
          setError(field, result.error || 'Invalid value');
          return false;
        } else {
          clearError(field);
          return true;
        }
      }
      return true;
    } else {
      // Validate all fields
      let isFormValid = true;
      const newErrors: Partial<Record<keyof T, string>> = {};

      for (const [fieldName, rule] of Object.entries(validationRules)) {
        const fieldKey = fieldName as keyof T;
        const result = rule(values[fieldKey]);
        if (!result.isValid) {
          newErrors[fieldKey] = result.error || 'Invalid value';
          isFormValid = false;
        }
      }

      setErrorsState(newErrors);
      return isFormValid;
    }
  }, [values, validationRules, setError, clearError]);

  const handleSubmit = useCallback(async () => {
    if (!onSubmit) return;

    const isFormValid = validate();
    if (!isFormValid) return;

    setIsSubmitting(true);
    try {
      await onSubmit(values);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [validate, onSubmit, values]);

  const reset = useCallback(() => {
    setValuesState(initialValues);
    setErrorsState({});
    setIsSubmitting(false);
  }, [initialValues]);

  const isValid = Object.keys(errors).length === 0;

  return {
    values,
    errors,
    isValid,
    isSubmitting,
    setValue,
    setValues,
    setError,
    clearError,
    clearErrors,
    validate,
    handleSubmit,
    reset,
  };
}
