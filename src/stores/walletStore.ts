// Wallet store using Zustand
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Balance, Transaction, PaymentMethod, WalletState } from '../types';
import { walletApi, paymentApi } from '../services/api';

interface WalletStore extends WalletState {
  // Actions
  fetchBalance: () => Promise<void>;
  fetchTransactions: (page?: number, limit?: number) => Promise<void>;
  fetchPaymentMethods: () => Promise<void>;
  addTransaction: (transaction: Transaction) => void;
  updateBalance: (balance: Balance) => void;
  clearError: () => void;
  refreshWallet: () => Promise<void>;
}

export const useWalletStore = create<WalletStore>()(
  persist(
    (set, get) => ({
      // Initial state
      balance: null,
      transactions: [],
      paymentMethods: [],
      isLoading: false,
      error: null,

      // Actions
      fetchBalance: async () => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await walletApi.getBalance();
          
          if (response.success && response.data) {
            set({
              balance: response.data as Balance,
              isLoading: false,
            });
          } else {
            set({
              isLoading: false,
              error: response.error || 'Failed to fetch balance',
            });
          }
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch balance',
          });
        }
      },

      fetchTransactions: async (page = 1, limit = 20) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await walletApi.getTransactions(page, limit);
          
          if (response.success && response.data) {
            const { data: transactions } = response.data as any;
            
            set((state) => ({
              transactions: page === 1 ? transactions : [...state.transactions, ...transactions],
              isLoading: false,
            }));
          } else {
            set({
              isLoading: false,
              error: response.error || 'Failed to fetch transactions',
            });
          }
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch transactions',
          });
        }
      },

      fetchPaymentMethods: async () => {
        try {
          const response = await walletApi.getPaymentMethods();
          
          if (response.success && response.data) {
            set({
              paymentMethods: response.data as PaymentMethod[],
            });
          }
        } catch (error) {
          console.error('Failed to fetch payment methods:', error);
        }
      },

      addTransaction: (transaction: Transaction) => {
        set((state) => ({
          transactions: [transaction, ...state.transactions],
        }));
      },

      updateBalance: (balance: Balance) => {
        set({ balance });
      },

      clearError: () => {
        set({ error: null });
      },

      refreshWallet: async () => {
        const { fetchBalance, fetchTransactions, fetchPaymentMethods } = get();
        
        await Promise.all([
          fetchBalance(),
          fetchTransactions(1, 20),
          fetchPaymentMethods(),
        ]);
      },
    }),
    {
      name: 'wallet-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        balance: state.balance,
        transactions: state.transactions.slice(0, 50), // Keep only recent transactions
        paymentMethods: state.paymentMethods,
      }),
    }
  )
);

// Payment store for payment-specific state
interface PaymentStore {
  isProcessing: boolean;
  currentPayment: any;
  qrCode: string | null;
  error: string | null;
  
  // Actions
  makePayment: (paymentData: any) => Promise<boolean>;
  generateQR: (amount?: string, description?: string) => Promise<string | null>;
  scanQR: (qrCode: string) => Promise<any>;
  clearPayment: () => void;
  setError: (error: string | null) => void;
}

export const usePaymentStore = create<PaymentStore>((set, get) => ({
  // Initial state
  isProcessing: false,
  currentPayment: null,
  qrCode: null,
  error: null,

  // Actions
  makePayment: async (paymentData: any) => {
    set({ isProcessing: true, error: null });
    
    try {
      const response = await paymentApi.makePayment(paymentData);
      
      if (response.success && response.data) {
        set({
          currentPayment: response.data,
          isProcessing: false,
        });
        
        // Add transaction to wallet store
        const walletStore = useWalletStore.getState();
        walletStore.addTransaction(response.data as Transaction);
        
        return true;
      } else {
        set({
          isProcessing: false,
          error: response.error || 'Payment failed',
        });
        return false;
      }
    } catch (error) {
      set({
        isProcessing: false,
        error: error instanceof Error ? error.message : 'Payment failed',
      });
      return false;
    }
  },

  generateQR: async (amount?: string, description?: string) => {
    try {
      const response = await paymentApi.generateQR(amount, description);
      
      if (response.success && response.data) {
        const qrCode = (response.data as any).qrCode;
        set({ qrCode });
        return qrCode;
      } else {
        set({ error: response.error || 'Failed to generate QR code' });
        return null;
      }
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to generate QR code',
      });
      return null;
    }
  },

  scanQR: async (qrCode: string) => {
    try {
      const response = await paymentApi.scanQR(qrCode);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        set({ error: response.error || 'Invalid QR code' });
        return null;
      }
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to scan QR code',
      });
      return null;
    }
  },

  clearPayment: () => {
    set({
      currentPayment: null,
      qrCode: null,
      error: null,
    });
  },

  setError: (error: string | null) => {
    set({ error });
  },
}));

// Helper hooks
export const useWallet = () => {
  const store = useWalletStore();
  return {
    balance: store.balance,
    transactions: store.transactions,
    paymentMethods: store.paymentMethods,
    isLoading: store.isLoading,
    error: store.error,
    fetchBalance: store.fetchBalance,
    fetchTransactions: store.fetchTransactions,
    refreshWallet: store.refreshWallet,
    clearError: store.clearError,
  };
};

export const usePayment = () => {
  const store = usePaymentStore();
  return {
    isProcessing: store.isProcessing,
    currentPayment: store.currentPayment,
    qrCode: store.qrCode,
    error: store.error,
    makePayment: store.makePayment,
    generateQR: store.generateQR,
    scanQR: store.scanQR,
    clearPayment: store.clearPayment,
    setError: store.setError,
  };
};
