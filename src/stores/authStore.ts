// Authentication store using Zustand
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, AuthState, LoginForm, RegisterForm } from '../types';
import { authApi } from '../services/api';
import { STORAGE_KEYS } from '../constants';

interface AuthStore extends AuthState {
  // Actions
  login: (credentials: LoginForm) => Promise<boolean>;
  register: (userData: RegisterForm) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
  forgotPin: (phoneNumber: string) => Promise<boolean>;
  resetPin: (token: string, newPin: string) => Promise<boolean>;
  clearError: () => void;
  setUser: (user: User) => void;
  updateUser: (userData: Partial<User>) => void;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginForm) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await authApi.login(credentials.phoneNumber, credentials.pin);
          
          if (response.success && response.data) {
            const { user, token } = response.data as any;
            
            // Store token
            await AsyncStorage.setItem(STORAGE_KEYS.USER_TOKEN, token);
            
            set({
              user,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
            
            return true;
          } else {
            set({
              isLoading: false,
              error: response.error || 'Login failed',
            });
            return false;
          }
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Login failed',
          });
          return false;
        }
      },

      register: async (userData: RegisterForm) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await authApi.register(userData);
          
          if (response.success && response.data) {
            const { user, token } = response.data as any;
            
            // Store token
            await AsyncStorage.setItem(STORAGE_KEYS.USER_TOKEN, token);
            
            set({
              user,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
            
            return true;
          } else {
            set({
              isLoading: false,
              error: response.error || 'Registration failed',
            });
            return false;
          }
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Registration failed',
          });
          return false;
        }
      },

      logout: async () => {
        set({ isLoading: true });
        
        try {
          await authApi.logout();
        } catch (error) {
          console.warn('Logout API call failed:', error);
        }
        
        // Clear stored data
        await AsyncStorage.multiRemove([
          STORAGE_KEYS.USER_TOKEN,
          STORAGE_KEYS.USER_DATA,
        ]);
        
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      },

      refreshToken: async () => {
        try {
          const token = await AsyncStorage.getItem(STORAGE_KEYS.USER_TOKEN);
          
          if (!token) {
            return false;
          }
          
          const response = await authApi.refreshToken(token);
          
          if (response.success && response.data) {
            const { user, token: newToken } = response.data as any;
            
            await AsyncStorage.setItem(STORAGE_KEYS.USER_TOKEN, newToken);
            
            set({
              user,
              isAuthenticated: true,
              error: null,
            });
            
            return true;
          }
          
          return false;
        } catch (error) {
          console.error('Token refresh failed:', error);
          return false;
        }
      },

      forgotPin: async (phoneNumber: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await authApi.forgotPin(phoneNumber);
          
          set({ isLoading: false });
          
          if (response.success) {
            return true;
          } else {
            set({ error: response.error || 'Failed to send reset code' });
            return false;
          }
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to send reset code',
          });
          return false;
        }
      },

      resetPin: async (token: string, newPin: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await authApi.resetPin(token, newPin);
          
          set({ isLoading: false });
          
          if (response.success) {
            return true;
          } else {
            set({ error: response.error || 'Failed to reset PIN' });
            return false;
          }
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to reset PIN',
          });
          return false;
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setUser: (user: User) => {
        set({ user, isAuthenticated: true });
      },

      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...userData },
          });
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Helper hooks
export const useAuth = () => {
  const store = useAuthStore();
  return {
    user: store.user,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    error: store.error,
    login: store.login,
    register: store.register,
    logout: store.logout,
    clearError: store.clearError,
  };
};

export const useUser = () => {
  const user = useAuthStore((state) => state.user);
  const updateUser = useAuthStore((state) => state.updateUser);
  const setUser = useAuthStore((state) => state.setUser);
  
  return {
    user,
    updateUser,
    setUser,
  };
};
