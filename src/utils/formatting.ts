// Formatting utilities based on Android reference app

export class FormattingUtils {
  // Currency formatting
  static formatCurrency(amount: string | number, currency: string = 'USD'): string {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    
    if (isNaN(numAmount)) {
      return `${currency} 0.00`;
    }

    return `${currency} ${numAmount.toFixed(2)}`;
  }

  // Phone number formatting
  static formatPhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    // Format based on length
    if (cleaned.length === 10) {
      return cleaned.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    } else if (cleaned.length === 11 && cleaned.startsWith('1')) {
      return cleaned.replace(/(\d{1})(\d{3})(\d{3})(\d{4})/, '+$1 ($2) $3-$4');
    } else if (cleaned.length > 10) {
      return `+${cleaned}`;
    }
    
    return phoneNumber;
  }

  // Card number formatting
  static formatCardNumber(cardNumber: string): string {
    // Remove all non-digit characters
    const cleaned = cardNumber.replace(/\D/g, '');
    
    // Add spaces every 4 digits
    return cleaned.replace(/(\d{4})(?=\d)/g, '$1 ');
  }

  // Expiry date formatting
  static formatExpiryDate(expiryDate: string): string {
    // Remove all non-digit characters
    const cleaned = expiryDate.replace(/\D/g, '');
    
    // Add slash after 2 digits
    if (cleaned.length >= 2) {
      return cleaned.substring(0, 2) + '/' + cleaned.substring(2, 4);
    }
    
    return cleaned;
  }

  // Date formatting
  static formatDate(date: string | Date, format: 'short' | 'long' | 'time' = 'short'): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    if (isNaN(dateObj.getTime())) {
      return 'Invalid Date';
    }

    switch (format) {
      case 'short':
        return dateObj.toLocaleDateString();
      case 'long':
        return dateObj.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
      case 'time':
        return dateObj.toLocaleString();
      default:
        return dateObj.toLocaleDateString();
    }
  }

  // Relative time formatting (e.g., "2 hours ago")
  static formatRelativeTime(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }

    const diffInWeeks = Math.floor(diffInDays / 7);
    if (diffInWeeks < 4) {
      return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;
    }

    return this.formatDate(dateObj, 'short');
  }

  // Transaction amount formatting with sign
  static formatTransactionAmount(amount: string | number, type: string, currency: string = 'USD'): string {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    const sign = type === 'topup' || type === 'deposit' ? '+' : '-';
    return `${sign}${this.formatCurrency(Math.abs(numAmount), currency)}`;
  }

  // Mask sensitive information
  static maskCardNumber(cardNumber: string): string {
    const cleaned = cardNumber.replace(/\D/g, '');
    if (cleaned.length < 4) return cardNumber;
    
    const lastFour = cleaned.slice(-4);
    const masked = '*'.repeat(cleaned.length - 4);
    return this.formatCardNumber(masked + lastFour);
  }

  static maskPhoneNumber(phoneNumber: string): string {
    const cleaned = phoneNumber.replace(/\D/g, '');
    if (cleaned.length < 4) return phoneNumber;
    
    const lastFour = cleaned.slice(-4);
    const masked = '*'.repeat(cleaned.length - 4);
    return this.formatPhoneNumber(masked + lastFour);
  }

  // Capitalize first letter of each word
  static capitalizeWords(text: string): string {
    return text.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  }

  // Format file size
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Format percentage
  static formatPercentage(value: number, decimals: number = 2): string {
    return `${(value * 100).toFixed(decimals)}%`;
  }

  // Format large numbers with abbreviations (K, M, B)
  static formatLargeNumber(num: number): string {
    if (num < 1000) return num.toString();
    
    const units = ['', 'K', 'M', 'B', 'T'];
    const unitIndex = Math.floor(Math.log10(Math.abs(num)) / 3);
    const unitValue = Math.pow(1000, unitIndex);
    const formattedValue = (num / unitValue).toFixed(1);
    
    return `${formattedValue}${units[unitIndex]}`;
  }

  // Remove formatting from currency
  static parseCurrency(formattedAmount: string): number {
    const cleaned = formattedAmount.replace(/[^\d.-]/g, '');
    return parseFloat(cleaned) || 0;
  }

  // Remove formatting from phone number
  static parsePhoneNumber(formattedPhone: string): string {
    return formattedPhone.replace(/\D/g, '');
  }

  // Remove formatting from card number
  static parseCardNumber(formattedCard: string): string {
    return formattedCard.replace(/\D/g, '');
  }

  // Format transaction reference
  static formatTransactionReference(ref: string): string {
    if (ref.length <= 8) return ref;
    return `${ref.substring(0, 4)}-${ref.substring(4, 8)}-${ref.substring(8)}`;
  }

  // Format account number
  static formatAccountNumber(accountNumber: string): string {
    const cleaned = accountNumber.replace(/\D/g, '');
    if (cleaned.length <= 4) return cleaned;
    
    // Show first 2 and last 4 digits
    const first = cleaned.substring(0, 2);
    const last = cleaned.substring(cleaned.length - 4);
    const middle = '*'.repeat(cleaned.length - 6);
    
    return `${first}${middle}${last}`;
  }
}
