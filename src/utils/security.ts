// Security utilities for the app
import * as Crypto from 'expo-crypto';
import * as LocalAuthentication from 'expo-local-authentication';

export class SecurityUtils {
  // Hash functions
  static async hashString(input: string): Promise<string> {
    try {
      return await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        input,
        { encoding: Crypto.CryptoEncoding.HEX }
      );
    } catch (error) {
      console.error('Error hashing string:', error);
      throw new Error('Failed to hash string');
    }
  }

  static async hashPin(pin: string, salt?: string): Promise<string> {
    const saltToUse = salt || await this.generateSalt();
    const combined = pin + saltToUse;
    return this.hashString(combined);
  }

  static async generateSalt(): Promise<string> {
    return await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      Math.random().toString() + Date.now().toString(),
      { encoding: Crypto.CryptoEncoding.HEX }
    );
  }

  // Biometric authentication
  static async isBiometricAvailable(): Promise<boolean> {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      return hasHardware && isEnrolled;
    } catch (error) {
      console.error('Error checking biometric availability:', error);
      return false;
    }
  }

  static async getBiometricTypes(): Promise<LocalAuthentication.AuthenticationType[]> {
    try {
      return await LocalAuthentication.supportedAuthenticationTypesAsync();
    } catch (error) {
      console.error('Error getting biometric types:', error);
      return [];
    }
  }

  static async authenticateWithBiometrics(
    promptMessage: string = 'Authenticate to continue'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const isAvailable = await this.isBiometricAvailable();
      
      if (!isAvailable) {
        return {
          success: false,
          error: 'Biometric authentication is not available',
        };
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage,
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use PIN',
        disableDeviceFallback: false,
      });

      if (result.success) {
        return { success: true };
      } else {
        return {
          success: false,
          error: result.error || 'Authentication failed',
        };
      }
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return {
        success: false,
        error: 'Authentication failed',
      };
    }
  }

  // PIN validation and security
  static validatePinStrength(pin: string): {
    isStrong: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    if (pin.length !== 4) {
      issues.push('PIN must be exactly 4 digits');
    }

    if (!/^\d+$/.test(pin)) {
      issues.push('PIN must contain only numbers');
    }

    // Check for common weak patterns
    if (pin === '0000' || pin === '1111' || pin === '2222' || 
        pin === '3333' || pin === '4444' || pin === '5555' ||
        pin === '6666' || pin === '7777' || pin === '8888' || pin === '9999') {
      issues.push('PIN should not be all the same digit');
    }

    if (pin === '1234' || pin === '4321' || pin === '0123' || pin === '3210') {
      issues.push('PIN should not be a simple sequence');
    }

    const currentYear = new Date().getFullYear().toString().slice(-2);
    if (pin.includes(currentYear)) {
      issues.push('PIN should not contain the current year');
    }

    return {
      isStrong: issues.length === 0,
      issues,
    };
  }

  // Session management
  static generateSessionId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  static isSessionExpired(sessionTimestamp: number, expiryMinutes: number = 30): boolean {
    const now = Date.now();
    const expiryTime = sessionTimestamp + (expiryMinutes * 60 * 1000);
    return now > expiryTime;
  }

  // Data sanitization
  static sanitizeInput(input: string): string {
    // Remove potentially dangerous characters
    return input.replace(/[<>\"'%;()&+]/g, '');
  }

  static sanitizePhoneNumber(phoneNumber: string): string {
    // Keep only digits and + sign
    return phoneNumber.replace(/[^\d+]/g, '');
  }

  static sanitizeAmount(amount: string): string {
    // Keep only digits and decimal point
    return amount.replace(/[^\d.]/g, '');
  }

  // Encryption utilities (for sensitive data)
  static async encryptData(data: string, key: string): Promise<string> {
    try {
      // Simple XOR encryption for demonstration
      // In production, use proper encryption libraries
      const keyHash = await this.hashString(key);
      let encrypted = '';
      
      for (let i = 0; i < data.length; i++) {
        const keyChar = keyHash[i % keyHash.length];
        const dataChar = data[i];
        encrypted += String.fromCharCode(dataChar.charCodeAt(0) ^ keyChar.charCodeAt(0));
      }
      
      return btoa(encrypted); // Base64 encode
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  static async decryptData(encryptedData: string, key: string): Promise<string> {
    try {
      const keyHash = await this.hashString(key);
      const encrypted = atob(encryptedData); // Base64 decode
      let decrypted = '';
      
      for (let i = 0; i < encrypted.length; i++) {
        const keyChar = keyHash[i % keyHash.length];
        const encryptedChar = encrypted[i];
        decrypted += String.fromCharCode(encryptedChar.charCodeAt(0) ^ keyChar.charCodeAt(0));
      }
      
      return decrypted;
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  // Security headers for API requests
  static generateSecurityHeaders(): Record<string, string> {
    return {
      'X-Request-ID': this.generateSessionId(),
      'X-Timestamp': Date.now().toString(),
      'X-Client-Version': '1.0.0',
      'X-Platform': 'mobile',
    };
  }

  // Rate limiting
  private static requestCounts: Map<string, { count: number; resetTime: number }> = new Map();

  static isRateLimited(identifier: string, maxRequests: number = 5, windowMinutes: number = 1): boolean {
    const now = Date.now();
    const windowMs = windowMinutes * 60 * 1000;
    
    const record = this.requestCounts.get(identifier);
    
    if (!record || now > record.resetTime) {
      this.requestCounts.set(identifier, {
        count: 1,
        resetTime: now + windowMs,
      });
      return false;
    }
    
    if (record.count >= maxRequests) {
      return true;
    }
    
    record.count++;
    return false;
  }

  // Device fingerprinting
  static async generateDeviceFingerprint(): Promise<string> {
    try {
      // Combine various device characteristics
      const timestamp = Date.now().toString();
      const random = Math.random().toString();
      const userAgent = navigator.userAgent || 'unknown';
      
      const combined = timestamp + random + userAgent;
      return await this.hashString(combined);
    } catch (error) {
      console.error('Error generating device fingerprint:', error);
      return 'unknown';
    }
  }

  // Secure random number generation
  static generateSecureRandom(length: number = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }

  // Validate transaction integrity
  static async validateTransactionIntegrity(
    transactionData: any,
    signature: string,
    publicKey: string
  ): Promise<boolean> {
    try {
      // In a real implementation, you would use proper digital signature verification
      const dataHash = await this.hashString(JSON.stringify(transactionData));
      const expectedSignature = await this.hashString(dataHash + publicKey);
      
      return signature === expectedSignature;
    } catch (error) {
      console.error('Transaction validation error:', error);
      return false;
    }
  }
}
