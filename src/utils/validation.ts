// Validation utilities based on Android reference app
import { VALIDATION_RULES } from '../constants';

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export class ValidationUtils {
  static validatePhoneNumber(phoneNumber: string): ValidationResult {
    if (!phoneNumber) {
      return { isValid: false, error: 'Phone number is required' };
    }

    if (phoneNumber.length < VALIDATION_RULES.PHONE_NUMBER.MIN_LENGTH) {
      return { isValid: false, error: 'Phone number is too short' };
    }

    if (phoneNumber.length > VALIDATION_RULES.PHONE_NUMBER.MAX_LENGTH) {
      return { isValid: false, error: 'Phone number is too long' };
    }

    if (!VALIDATION_RULES.PHONE_NUMBER.PATTERN.test(phoneNumber)) {
      return { isValid: false, error: 'Please enter a valid phone number' };
    }

    return { isValid: true };
  }

  static validatePin(pin: string): ValidationResult {
    if (!pin) {
      return { isValid: false, error: 'PIN is required' };
    }

    if (pin.length !== VALIDATION_RULES.PIN.LENGTH) {
      return { isValid: false, error: 'PIN must be 4 digits' };
    }

    if (!VALIDATION_RULES.PIN.PATTERN.test(pin)) {
      return { isValid: false, error: 'PIN must contain only numbers' };
    }

    return { isValid: true };
  }

  static validateAmount(amount: string): ValidationResult {
    if (!amount) {
      return { isValid: false, error: 'Amount is required' };
    }

    const numAmount = parseFloat(amount);

    if (isNaN(numAmount)) {
      return { isValid: false, error: 'Please enter a valid amount' };
    }

    if (numAmount < VALIDATION_RULES.AMOUNT.MIN) {
      return { isValid: false, error: `Minimum amount is ${VALIDATION_RULES.AMOUNT.MIN}` };
    }

    if (numAmount > VALIDATION_RULES.AMOUNT.MAX) {
      return { isValid: false, error: `Maximum amount is ${VALIDATION_RULES.AMOUNT.MAX}` };
    }

    if (!VALIDATION_RULES.AMOUNT.PATTERN.test(amount)) {
      return { isValid: false, error: 'Amount can have maximum 2 decimal places' };
    }

    return { isValid: true };
  }

  static validateName(name: string): ValidationResult {
    if (!name) {
      return { isValid: false, error: 'Name is required' };
    }

    if (name.length < VALIDATION_RULES.NAME.MIN_LENGTH) {
      return { isValid: false, error: 'Name is too short' };
    }

    if (name.length > VALIDATION_RULES.NAME.MAX_LENGTH) {
      return { isValid: false, error: 'Name is too long' };
    }

    if (!VALIDATION_RULES.NAME.PATTERN.test(name)) {
      return { isValid: false, error: 'Name can only contain letters and spaces' };
    }

    return { isValid: true };
  }

  static validateEmail(email: string): ValidationResult {
    if (!email) {
      return { isValid: false, error: 'Email is required' };
    }

    if (!VALIDATION_RULES.EMAIL.PATTERN.test(email)) {
      return { isValid: false, error: 'Please enter a valid email address' };
    }

    return { isValid: true };
  }

  static validateConfirmPin(pin: string, confirmPin: string): ValidationResult {
    if (!confirmPin) {
      return { isValid: false, error: 'Please confirm your PIN' };
    }

    if (pin !== confirmPin) {
      return { isValid: false, error: 'PINs do not match' };
    }

    return { isValid: true };
  }

  static validateRequired(value: string, fieldName: string): ValidationResult {
    if (!value || value.trim() === '') {
      return { isValid: false, error: `${fieldName} is required` };
    }

    return { isValid: true };
  }

  static validateCardNumber(cardNumber: string): ValidationResult {
    if (!cardNumber) {
      return { isValid: false, error: 'Card number is required' };
    }

    // Remove spaces and dashes
    const cleanCardNumber = cardNumber.replace(/[\s-]/g, '');

    if (cleanCardNumber.length < 13 || cleanCardNumber.length > 19) {
      return { isValid: false, error: 'Card number must be between 13 and 19 digits' };
    }

    if (!/^\d+$/.test(cleanCardNumber)) {
      return { isValid: false, error: 'Card number must contain only digits' };
    }

    // Luhn algorithm validation
    if (!this.luhnCheck(cleanCardNumber)) {
      return { isValid: false, error: 'Invalid card number' };
    }

    return { isValid: true };
  }

  static validateExpiryDate(expiryDate: string): ValidationResult {
    if (!expiryDate) {
      return { isValid: false, error: 'Expiry date is required' };
    }

    const expiryPattern = /^(0[1-9]|1[0-2])\/\d{2}$/;
    if (!expiryPattern.test(expiryDate)) {
      return { isValid: false, error: 'Expiry date must be in MM/YY format' };
    }

    const [month, year] = expiryDate.split('/');
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear() % 100;
    const currentMonth = currentDate.getMonth() + 1;

    const expYear = parseInt(year, 10);
    const expMonth = parseInt(month, 10);

    if (expYear < currentYear || (expYear === currentYear && expMonth < currentMonth)) {
      return { isValid: false, error: 'Card has expired' };
    }

    return { isValid: true };
  }

  static validateCVV(cvv: string): ValidationResult {
    if (!cvv) {
      return { isValid: false, error: 'CVV is required' };
    }

    if (!/^\d{3,4}$/.test(cvv)) {
      return { isValid: false, error: 'CVV must be 3 or 4 digits' };
    }

    return { isValid: true };
  }

  // Luhn algorithm for card number validation
  private static luhnCheck(cardNumber: string): boolean {
    let sum = 0;
    let isEven = false;

    for (let i = cardNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cardNumber.charAt(i), 10);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 === 0;
  }

  // Validate multiple fields at once
  static validateForm(fields: Record<string, any>, rules: Record<string, (value: any) => ValidationResult>): {
    isValid: boolean;
    errors: Record<string, string>;
  } {
    const errors: Record<string, string> = {};
    let isValid = true;

    for (const [fieldName, value] of Object.entries(fields)) {
      if (rules[fieldName]) {
        const result = rules[fieldName](value);
        if (!result.isValid) {
          errors[fieldName] = result.error || 'Invalid value';
          isValid = false;
        }
      }
    }

    return { isValid, errors };
  }
}
