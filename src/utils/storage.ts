// Storage utilities for secure data persistence
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../constants';

export class StorageUtils {
  // Generic storage methods
  static async setItem(key: string, value: any): Promise<void> {
    try {
      const jsonValue = JSON.stringify(value);
      await AsyncStorage.setItem(key, jsonValue);
    } catch (error) {
      console.error('Error storing data:', error);
      throw new Error('Failed to store data');
    }
  }

  static async getItem<T>(key: string): Promise<T | null> {
    try {
      const jsonValue = await AsyncStorage.getItem(key);
      return jsonValue != null ? JSON.parse(jsonValue) : null;
    } catch (error) {
      console.error('Error retrieving data:', error);
      return null;
    }
  }

  static async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error('Error removing data:', error);
      throw new Error('Failed to remove data');
    }
  }

  static async clear(): Promise<void> {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Error clearing storage:', error);
      throw new Error('Failed to clear storage');
    }
  }

  static async getAllKeys(): Promise<string[]> {
    try {
      return await AsyncStorage.getAllKeys();
    } catch (error) {
      console.error('Error getting all keys:', error);
      return [];
    }
  }

  static async multiSet(keyValuePairs: [string, any][]): Promise<void> {
    try {
      const jsonPairs: [string, string][] = keyValuePairs.map(([key, value]) => [
        key,
        JSON.stringify(value),
      ]);
      await AsyncStorage.multiSet(jsonPairs);
    } catch (error) {
      console.error('Error storing multiple items:', error);
      throw new Error('Failed to store multiple items');
    }
  }

  static async multiGet(keys: string[]): Promise<Record<string, any>> {
    try {
      const keyValuePairs = await AsyncStorage.multiGet(keys);
      const result: Record<string, any> = {};
      
      keyValuePairs.forEach(([key, value]) => {
        if (value != null) {
          try {
            result[key] = JSON.parse(value);
          } catch {
            result[key] = value;
          }
        }
      });
      
      return result;
    } catch (error) {
      console.error('Error retrieving multiple items:', error);
      return {};
    }
  }

  static async multiRemove(keys: string[]): Promise<void> {
    try {
      await AsyncStorage.multiRemove(keys);
    } catch (error) {
      console.error('Error removing multiple items:', error);
      throw new Error('Failed to remove multiple items');
    }
  }

  // App-specific storage methods
  static async setUserToken(token: string): Promise<void> {
    return this.setItem(STORAGE_KEYS.USER_TOKEN, token);
  }

  static async getUserToken(): Promise<string | null> {
    return this.getItem<string>(STORAGE_KEYS.USER_TOKEN);
  }

  static async removeUserToken(): Promise<void> {
    return this.removeItem(STORAGE_KEYS.USER_TOKEN);
  }

  static async setUserData(userData: any): Promise<void> {
    return this.setItem(STORAGE_KEYS.USER_DATA, userData);
  }

  static async getUserData(): Promise<any> {
    return this.getItem(STORAGE_KEYS.USER_DATA);
  }

  static async removeUserData(): Promise<void> {
    return this.removeItem(STORAGE_KEYS.USER_DATA);
  }

  static async setBiometricEnabled(enabled: boolean): Promise<void> {
    return this.setItem(STORAGE_KEYS.BIOMETRIC_ENABLED, enabled);
  }

  static async isBiometricEnabled(): Promise<boolean> {
    const enabled = await this.getItem<boolean>(STORAGE_KEYS.BIOMETRIC_ENABLED);
    return enabled ?? false;
  }

  static async setLanguage(language: string): Promise<void> {
    return this.setItem(STORAGE_KEYS.LANGUAGE, language);
  }

  static async getLanguage(): Promise<string> {
    const language = await this.getItem<string>(STORAGE_KEYS.LANGUAGE);
    return language ?? 'en';
  }

  static async setTheme(theme: string): Promise<void> {
    return this.setItem(STORAGE_KEYS.THEME, theme);
  }

  static async getTheme(): Promise<string> {
    const theme = await this.getItem<string>(STORAGE_KEYS.THEME);
    return theme ?? 'light';
  }

  static async setRecentContacts(contacts: any[]): Promise<void> {
    return this.setItem(STORAGE_KEYS.RECENT_CONTACTS, contacts);
  }

  static async getRecentContacts(): Promise<any[]> {
    const contacts = await this.getItem<any[]>(STORAGE_KEYS.RECENT_CONTACTS);
    return contacts ?? [];
  }

  static async addRecentContact(contact: any): Promise<void> {
    const recentContacts = await this.getRecentContacts();
    
    // Remove if already exists
    const filteredContacts = recentContacts.filter(c => c.id !== contact.id);
    
    // Add to beginning
    const updatedContacts = [contact, ...filteredContacts].slice(0, 10); // Keep only 10 recent
    
    return this.setRecentContacts(updatedContacts);
  }

  static async setPaymentMethods(paymentMethods: any[]): Promise<void> {
    return this.setItem(STORAGE_KEYS.PAYMENT_METHODS, paymentMethods);
  }

  static async getPaymentMethods(): Promise<any[]> {
    const paymentMethods = await this.getItem<any[]>(STORAGE_KEYS.PAYMENT_METHODS);
    return paymentMethods ?? [];
  }

  // Clear all user-related data (for logout)
  static async clearUserData(): Promise<void> {
    const userKeys = [
      STORAGE_KEYS.USER_TOKEN,
      STORAGE_KEYS.USER_DATA,
      STORAGE_KEYS.RECENT_CONTACTS,
      STORAGE_KEYS.PAYMENT_METHODS,
    ];
    
    return this.multiRemove(userKeys);
  }

  // Check if storage is available
  static async isStorageAvailable(): Promise<boolean> {
    try {
      const testKey = '__storage_test__';
      const testValue = 'test';
      
      await AsyncStorage.setItem(testKey, testValue);
      const retrievedValue = await AsyncStorage.getItem(testKey);
      await AsyncStorage.removeItem(testKey);
      
      return retrievedValue === testValue;
    } catch {
      return false;
    }
  }

  // Get storage usage info
  static async getStorageInfo(): Promise<{
    totalKeys: number;
    estimatedSize: number;
  }> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const keyValuePairs = await AsyncStorage.multiGet(keys);
      
      let estimatedSize = 0;
      keyValuePairs.forEach(([key, value]) => {
        estimatedSize += key.length + (value?.length || 0);
      });
      
      return {
        totalKeys: keys.length,
        estimatedSize,
      };
    } catch {
      return {
        totalKeys: 0,
        estimatedSize: 0,
      };
    }
  }

  // Backup and restore functionality
  static async exportData(): Promise<string> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const data = await this.multiGet(keys);
      return JSON.stringify(data);
    } catch (error) {
      console.error('Error exporting data:', error);
      throw new Error('Failed to export data');
    }
  }

  static async importData(jsonData: string): Promise<void> {
    try {
      const data = JSON.parse(jsonData);
      const keyValuePairs: [string, any][] = Object.entries(data);
      await this.multiSet(keyValuePairs);
    } catch (error) {
      console.error('Error importing data:', error);
      throw new Error('Failed to import data');
    }
  }
}
