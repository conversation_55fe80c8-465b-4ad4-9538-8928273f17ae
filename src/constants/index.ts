// App constants based on Android reference

export const APP_CONFIG = {
  APP_NAME: 'OneMoney',
  VERSION: '1.0.0',
  API_TIMEOUT: 30000,
  MAX_RETRY_ATTEMPTS: 3,
} as const;

export const CURRENCY = {
  USD: 'USD',
  ZWL: 'ZWL',
  ZAR: 'ZAR',
} as const;

export const TRANSACTION_TYPES = {
  PAYMENT: 'payment',
  TRANSFER: 'transfer',
  TOPUP: 'topup',
  WITHDRAWAL: 'withdrawal',
  BILL_PAYMENT: 'bill_payment',
  AIRTIME: 'airtime',
} as const;

export const TRANSACTION_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
} as const;

export const PAYMENT_METHODS = {
  WALLET: 'wallet',
  BANK_CARD: 'bank_card',
  MOBILE_MONEY: 'mobile_money',
} as const;

export const BILL_TYPES = {
  ELECTRICITY: 'electricity',
  WATER: 'water',
  INTERNET: 'internet',
  SCHOOL: 'school',
  MUNICIPAL: 'municipal',
  OTHER: 'other',
} as const;

export const AIRTIME_PROVIDERS = {
  ECONET: 'econet',
  NETONE: 'netone',
  TELECEL: 'telecel',
} as const;

export const CARD_TYPES = {
  VISA: 'visa',
  MASTERCARD: 'mastercard',
  LOCAL: 'local',
} as const;

export const ACCOUNT_TYPES = {
  BASIC: 'basic',
  PREMIUM: 'premium',
} as const;

export const VALIDATION_RULES = {
  PHONE_NUMBER: {
    MIN_LENGTH: 10,
    MAX_LENGTH: 15,
    PATTERN: /^[+]?[0-9]{10,15}$/,
  },
  PIN: {
    LENGTH: 4,
    PATTERN: /^[0-9]{4}$/,
  },
  AMOUNT: {
    MIN: 0.01,
    MAX: 10000,
    PATTERN: /^\d+(\.\d{1,2})?$/,
  },
  NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 50,
    PATTERN: /^[a-zA-Z\s]+$/,
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
} as const;

export const STORAGE_KEYS = {
  USER_TOKEN: 'user_token',
  USER_DATA: 'user_data',
  BIOMETRIC_ENABLED: 'biometric_enabled',
  LANGUAGE: 'language',
  THEME: 'theme',
  RECENT_CONTACTS: 'recent_contacts',
  PAYMENT_METHODS: 'payment_methods',
} as const;

export const API_ENDPOINTS = {
  // Auth endpoints
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  LOGOUT: '/auth/logout',
  REFRESH_TOKEN: '/auth/refresh',
  FORGOT_PIN: '/auth/forgot-pin',
  RESET_PIN: '/auth/reset-pin',
  
  // User endpoints
  PROFILE: '/user/profile',
  UPDATE_PROFILE: '/user/update',
  CHANGE_PIN: '/user/change-pin',
  
  // Wallet endpoints
  BALANCE: '/wallet/balance',
  TRANSACTIONS: '/wallet/transactions',
  PAYMENT_METHODS: '/wallet/payment-methods',
  
  // Payment endpoints
  MAKE_PAYMENT: '/payments/make',
  QR_GENERATE: '/payments/qr/generate',
  QR_SCAN: '/payments/qr/scan',
  BILL_PAYMENT: '/payments/bills',
  AIRTIME: '/payments/airtime',
  
  // Transfer endpoints
  SEND_MONEY: '/transfers/send',
  TRANSFER_HISTORY: '/transfers/history',
  CONTACTS: '/transfers/contacts',
  
  // Top-up endpoints
  TOPUP: '/topup',
  BANK_CARDS: '/topup/cards',
  ADD_CARD: '/topup/cards/add',
  
  // Utility endpoints
  EXCHANGE_RATES: '/utils/exchange-rates',
  FEES: '/utils/fees',
  BANKS: '/utils/banks',
  PROVIDERS: '/utils/providers',
} as const;

export const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INSUFFICIENT_FUNDS: 'INSUFFICIENT_FUNDS',
  TRANSACTION_FAILED: 'TRANSACTION_FAILED',
  CARD_DECLINED: 'CARD_DECLINED',
  INVALID_PIN: 'INVALID_PIN',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
} as const;

export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Login successful',
  REGISTRATION_SUCCESS: 'Registration successful',
  PAYMENT_SUCCESS: 'Payment completed successfully',
  TRANSFER_SUCCESS: 'Transfer completed successfully',
  TOPUP_SUCCESS: 'Top-up completed successfully',
  PROFILE_UPDATED: 'Profile updated successfully',
  PIN_CHANGED: 'PIN changed successfully',
  CARD_ADDED: 'Card added successfully',
} as const;

export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection error. Please try again.',
  INVALID_CREDENTIALS: 'Invalid phone number or PIN',
  INSUFFICIENT_FUNDS: 'Insufficient funds in your account',
  TRANSACTION_FAILED: 'Transaction failed. Please try again.',
  INVALID_AMOUNT: 'Please enter a valid amount',
  INVALID_PHONE: 'Please enter a valid phone number',
  INVALID_PIN: 'PIN must be 4 digits',
  CARD_EXPIRED: 'Card has expired',
  CARD_DECLINED: 'Card was declined',
  SERVICE_UNAVAILABLE: 'Service temporarily unavailable',
  GENERIC_ERROR: 'Something went wrong. Please try again.',
} as const;

export const COLORS = {
  PRIMARY: '#007AFF',
  SECONDARY: '#5856D6',
  SUCCESS: '#34C759',
  WARNING: '#FF9500',
  ERROR: '#FF3B30',
  INFO: '#5AC8FA',
  LIGHT: '#F2F2F7',
  DARK: '#1C1C1E',
  GRAY: '#8E8E93',
  WHITE: '#FFFFFF',
  BLACK: '#000000',
} as const;

export const FONTS = {
  REGULAR: 'System',
  MEDIUM: 'System-Medium',
  BOLD: 'System-Bold',
  LIGHT: 'System-Light',
} as const;

export const SPACING = {
  XS: 4,
  SM: 8,
  MD: 16,
  LG: 24,
  XL: 32,
  XXL: 48,
} as const;

export const BORDER_RADIUS = {
  SM: 4,
  MD: 8,
  LG: 12,
  XL: 16,
  FULL: 9999,
} as const;

export const ANIMATION_DURATION = {
  FAST: 200,
  NORMAL: 300,
  SLOW: 500,
} as const;
