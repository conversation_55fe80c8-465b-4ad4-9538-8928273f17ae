// API service layer based on Android reference architecture

import { API_ENDPOINTS, APP_CONFIG, ERROR_CODES } from '../constants';
import { ApiResponse, PaginatedResponse } from '../types';

class ApiService {
  private baseURL: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;

  constructor() {
    this.baseURL = process.env.EXPO_PUBLIC_API_URL || 'https://api.onemoney.com';
    this.timeout = APP_CONFIG.API_TIMEOUT;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const token = await this.getAuthToken();
      
      const headers = {
        ...this.defaultHeaders,
        ...options.headers,
        ...(token && { Authorization: `Bearer ${token}` }),
      };

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        ...options,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.message || 'Request failed',
          code: response.status,
        };
      }

      return {
        success: true,
        data: data.data || data,
        message: data.message,
      };
    } catch (error) {
      console.error('API Request Error:', error);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          return {
            success: false,
            error: 'Request timeout',
            code: 408,
          };
        }
        
        return {
          success: false,
          error: error.message,
          code: 0,
        };
      }

      return {
        success: false,
        error: 'Unknown error occurred',
        code: 0,
      };
    }
  }

  private async getAuthToken(): Promise<string | null> {
    // Implementation will be added when we create the auth store
    return null;
  }

  // GET request
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  // POST request
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PUT request
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // DELETE request
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // PATCH request
  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // Upload file
  async upload<T>(endpoint: string, file: FormData): Promise<ApiResponse<T>> {
    const token = await this.getAuthToken();
    
    const headers: Record<string, string> = {
      ...(token && { Authorization: `Bearer ${token}` }),
    };

    return this.request<T>(endpoint, {
      method: 'POST',
      headers,
      body: file,
    });
  }

  // Paginated request
  async getPaginated<T>(
    endpoint: string,
    page: number = 1,
    limit: number = 20
  ): Promise<ApiResponse<PaginatedResponse<T>>> {
    const url = `${endpoint}?page=${page}&limit=${limit}`;
    return this.get<PaginatedResponse<T>>(url);
  }
}

// Create singleton instance
export const apiService = new ApiService();

// Specific API methods based on Android reference
export const authApi = {
  login: (phoneNumber: string, pin: string) =>
    apiService.post(API_ENDPOINTS.LOGIN, { phoneNumber, pin }),
  
  register: (userData: any) =>
    apiService.post(API_ENDPOINTS.REGISTER, userData),
  
  logout: () =>
    apiService.post(API_ENDPOINTS.LOGOUT),
  
  refreshToken: (refreshToken: string) =>
    apiService.post(API_ENDPOINTS.REFRESH_TOKEN, { refreshToken }),
  
  forgotPin: (phoneNumber: string) =>
    apiService.post(API_ENDPOINTS.FORGOT_PIN, { phoneNumber }),
  
  resetPin: (token: string, newPin: string) =>
    apiService.post(API_ENDPOINTS.RESET_PIN, { token, newPin }),
};

export const userApi = {
  getProfile: () =>
    apiService.get(API_ENDPOINTS.PROFILE),
  
  updateProfile: (userData: any) =>
    apiService.put(API_ENDPOINTS.UPDATE_PROFILE, userData),
  
  changePin: (currentPin: string, newPin: string) =>
    apiService.post(API_ENDPOINTS.CHANGE_PIN, { currentPin, newPin }),
};

export const walletApi = {
  getBalance: () =>
    apiService.get(API_ENDPOINTS.BALANCE),
  
  getTransactions: (page?: number, limit?: number) =>
    apiService.getPaginated(API_ENDPOINTS.TRANSACTIONS, page, limit),
  
  getPaymentMethods: () =>
    apiService.get(API_ENDPOINTS.PAYMENT_METHODS),
};

export const paymentApi = {
  makePayment: (paymentData: any) =>
    apiService.post(API_ENDPOINTS.MAKE_PAYMENT, paymentData),
  
  generateQR: (amount?: string, description?: string) =>
    apiService.post(API_ENDPOINTS.QR_GENERATE, { amount, description }),
  
  scanQR: (qrCode: string) =>
    apiService.post(API_ENDPOINTS.QR_SCAN, { qrCode }),
  
  payBill: (billData: any) =>
    apiService.post(API_ENDPOINTS.BILL_PAYMENT, billData),
  
  buyAirtime: (airtimeData: any) =>
    apiService.post(API_ENDPOINTS.AIRTIME, airtimeData),
};

export const transferApi = {
  sendMoney: (transferData: any) =>
    apiService.post(API_ENDPOINTS.SEND_MONEY, transferData),
  
  getTransferHistory: (page?: number, limit?: number) =>
    apiService.getPaginated(API_ENDPOINTS.TRANSFER_HISTORY, page, limit),
  
  getContacts: () =>
    apiService.get(API_ENDPOINTS.CONTACTS),
};

export const topupApi = {
  topUp: (topupData: any) =>
    apiService.post(API_ENDPOINTS.TOPUP, topupData),
  
  getBankCards: () =>
    apiService.get(API_ENDPOINTS.BANK_CARDS),
  
  addBankCard: (cardData: any) =>
    apiService.post(API_ENDPOINTS.ADD_CARD, cardData),
};

export const utilityApi = {
  getExchangeRates: () =>
    apiService.get(API_ENDPOINTS.EXCHANGE_RATES),
  
  getFees: (transactionType: string, amount: string) =>
    apiService.get(`${API_ENDPOINTS.FEES}?type=${transactionType}&amount=${amount}`),
  
  getBanks: () =>
    apiService.get(API_ENDPOINTS.BANKS),
  
  getProviders: (type: string) =>
    apiService.get(`${API_ENDPOINTS.PROVIDERS}?type=${type}`),
};
