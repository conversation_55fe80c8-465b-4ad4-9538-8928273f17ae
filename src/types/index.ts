// Core data types based on Android reference app

export interface User {
  id: string;
  phoneNumber: string;
  firstName: string;
  lastName: string;
  email?: string;
  profileImage?: string;
  isVerified: boolean;
  accountType: 'basic' | 'premium';
  createdAt: string;
  updatedAt: string;
}

export interface Balance {
  balance: string;
  currType: string;
  basicAccountBalance: string;
  cashbackAccountBalance: string;
  isShow: boolean;
  isNeedActivate: boolean;
  totalAmt?: string;
  feeAmt?: string;
  taxAmt?: string;
  lastBalanceEnquiryTime?: string;
  isEnoughPay?: string;
}

export interface PaymentMethod {
  id: string;
  type: 'wallet' | 'bank_card' | 'mobile_money';
  name: string;
  identifier: string; // card number, phone number, etc.
  isDefault: boolean;
  isActive: boolean;
}

export interface Transaction {
  id: string;
  type: 'payment' | 'transfer' | 'topup' | 'withdrawal';
  amount: string;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  description: string;
  recipient?: Contact;
  paymentMethod?: PaymentMethod;
  fee?: string;
  tax?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Contact {
  id: string;
  name: string;
  phoneNumber: string;
  email?: string;
  profileImage?: string;
  isFavorite: boolean;
  lastTransactionDate?: string;
}

export interface BankCard {
  id: string;
  cardNumber: string;
  cardHolderName: string;
  expiryDate: string;
  bankName: string;
  cardType: 'visa' | 'mastercard' | 'local';
  isDefault: boolean;
  isActive: boolean;
}

export interface QRCode {
  id: string;
  code: string;
  amount?: string;
  description?: string;
  expiresAt?: string;
  isActive: boolean;
}

export interface BillPayment {
  id: string;
  type: 'electricity' | 'water' | 'internet' | 'school' | 'other';
  provider: string;
  accountNumber: string;
  amount: string;
  dueDate?: string;
  status: 'pending' | 'paid' | 'overdue';
}

export interface AirtimeBundle {
  id: string;
  provider: string;
  type: 'airtime' | 'data';
  amount: string;
  description: string;
  validity?: string;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Navigation types
export type RootStackParamList = {
  '(auth)': undefined;
  '(main)': undefined;
};

export type AuthStackParamList = {
  login: undefined;
  register: undefined;
  'forgot-pin': undefined;
};

export type MainStackParamList = {
  '(tabs)': undefined;
  payment: {
    screen: string;
    params?: any;
  };
  topup: {
    screen: string;
    params?: any;
  };
  transfer: {
    screen: string;
    params?: any;
  };
};

export type TabParamList = {
  home: undefined;
  payments: undefined;
  transfer: undefined;
  history: undefined;
  profile: undefined;
};

// Form types
export interface LoginForm {
  phoneNumber: string;
  pin: string;
}

export interface RegisterForm {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email?: string;
  pin: string;
  confirmPin: string;
  agreeToTerms: boolean;
}

export interface PaymentForm {
  amount: string;
  recipient: string;
  description?: string;
  paymentMethod: string;
}

export interface TransferForm {
  amount: string;
  recipient: Contact;
  description?: string;
  paymentMethod: string;
}

export interface TopUpForm {
  amount: string;
  paymentMethod: BankCard;
}

// State types
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface WalletState {
  balance: Balance | null;
  transactions: Transaction[];
  paymentMethods: PaymentMethod[];
  isLoading: boolean;
  error: string | null;
}

export interface ContactsState {
  contacts: Contact[];
  favorites: Contact[];
  recent: Contact[];
  isLoading: boolean;
  error: string | null;
}
